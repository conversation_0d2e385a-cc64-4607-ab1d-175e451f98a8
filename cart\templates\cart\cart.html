<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السلة | Best IN Click</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2 class="mb-4 text-center">سلة التسوق</h2>
        <form method="post">
            {% csrf_token %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                        <th>إزالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in cart.items %}
                    <tr>
                        <td>{{ item.product.name }}</td>
                        <td><input type="number" name="quantity_{{ item.id }}" value="{{ item.quantity }}" min="1" class="form-control" style="width:80px;"></td>
                        <td>{{ item.product.price }} ريال</td>
                        <td>{{ item.total_price }} ريال</td>
                        <td><button name="remove" value="{{ item.id }}" class="btn btn-danger btn-sm">حذف</button></td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="5" class="text-center">السلة فارغة.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
            <div class="d-flex justify-content-between align-items-center">
                <strong>الإجمالي الكلي: {{ cart.total }} ريال</strong>
                <button type="submit" class="btn btn-success">إتمام الطلب</button>
            </div>
        </form>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
