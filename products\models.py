"""
Product management models for Best on Click e-commerce platform.

This module defines comprehensive product, category, brand, and store models
with AI-optimized fields for recommendations, search, and analytics.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
import uuid

User = get_user_model()


class Category(models.Model):
    """
    Product category model with hierarchical support.
    
    Supports nested categories for organized product classification
    and enhanced AI-powered recommendations based on category relationships.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Category name for product classification"
    )
    slug = models.SlugField(
        max_length=120,
        unique=True,
        blank=True,
        help_text="URL-friendly category identifier"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed category description for SEO and user guidance"
    )
    
    # Hierarchical category support
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='subcategories',
        help_text="Parent category for hierarchical organization"
    )
    
    # AI optimization fields
    ai_keywords = models.JSONField(
        default=list,
        blank=True,
        help_text="Keywords for AI-powered search and recommendations"
    )
    
    # Metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Category visibility status"
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        help_text="Display order for category listing"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'product_categories'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['parent']),
            models.Index(fields=['sort_order']),
        ]
    
    def save(self, *args, **kwargs):
        """Auto-generate slug from name if not provided."""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
    
    @property
    def full_path(self):
        """Return full category path for hierarchical display."""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name


class Brand(models.Model):
    """
    Brand model for product manufacturer/brand information.
    
    Stores brand details with AI-optimized fields for brand-based
    recommendations and comparative analysis.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Brand name"
    )
    slug = models.SlugField(
        max_length=120,
        unique=True,
        blank=True,
        help_text="URL-friendly brand identifier"
    )
    description = models.TextField(
        blank=True,
        help_text="Brand description and history"
    )
    logo_url = models.URLField(
        blank=True,
        help_text="Brand logo image URL"
    )
    website = models.URLField(
        blank=True,
        help_text="Official brand website"
    )
    
    # AI optimization fields
    brand_reputation_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(10.0)],
        help_text="AI-calculated brand reputation score (0-10)"
    )
    
    # Metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Brand visibility status"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'product_brands'
        verbose_name = 'Brand'
        verbose_name_plural = 'Brands'
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['brand_reputation_score']),
        ]
    
    def save(self, *args, **kwargs):
        """Auto-generate slug from name if not provided."""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name


class Store(models.Model):
    """
    Store model for multi-vendor e-commerce support.
    
    Represents individual stores/vendors with comprehensive
    information for AI-powered store comparisons and analytics.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='owned_stores',
        help_text="Store owner/manager"
    )
    
    # Basic store information
    name = models.CharField(
        max_length=200,
        help_text="Store name"
    )
    slug = models.SlugField(
        max_length=220,
        unique=True,
        blank=True,
        help_text="URL-friendly store identifier"
    )
    description = models.TextField(
        help_text="Store description and policies"
    )
    
    # Contact information
    contact_email = models.EmailField(
        help_text="Store contact email"
    )
    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Store contact phone number"
    )
    
    # Address information
    address_line_1 = models.CharField(max_length=255)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100, default='USA')
    
    # AI-powered analytics fields
    average_rating = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(5.0)],
        help_text="Average customer rating (0-5 stars)"
    )
    total_orders_count = models.PositiveIntegerField(
        default=0,
        help_text="Total number of orders processed"
    )
    customer_service_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(10.0)],
        help_text="AI-calculated customer service quality score (0-10)"
    )
    
    # Store status and metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Store operational status"
    )
    is_verified = models.BooleanField(
        default=False,
        help_text="Store verification status"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'stores'
        verbose_name = 'Store'
        verbose_name_plural = 'Stores'
        ordering = ['-average_rating', 'name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['average_rating']),
            models.Index(fields=['customer_service_score']),
        ]
    
    def save(self, *args, **kwargs):
        """Auto-generate slug from name if not provided."""
        if not self.slug:
            base_slug = slugify(self.name)
            slug = base_slug
            counter = 1
            while Store.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
    
    @property
    def full_address(self):
        """Return formatted full address."""
        address_parts = [
            self.address_line_1,
            self.address_line_2,
            self.city,
            self.state,
            self.postal_code,
            self.country
        ]
        return ', '.join(filter(None, address_parts))


class Product(models.Model):
    """
    Comprehensive product model with AI optimization.
    
    Central product model with extensive fields for AI-powered
    recommendations, search, comparisons, and analytics.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic product information
    name = models.CharField(
        max_length=200,
        help_text="Product name"
    )
    slug = models.SlugField(
        max_length=220,
        unique=True,
        blank=True,
        help_text="URL-friendly product identifier"
    )
    description = models.TextField(
        help_text="Detailed product description"
    )
    short_description = models.CharField(
        max_length=500,
        blank=True,
        help_text="Brief product summary for listings"
    )
    
    # Product relationships
    store = models.ForeignKey(
        Store,
        on_delete=models.CASCADE,
        related_name='products',
        help_text="Store selling this product"
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        related_name='products',
        help_text="Product category"
    )
    brand = models.ForeignKey(
        Brand,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='products',
        help_text="Product brand"
    )
    
    # Pricing information
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0.01)],
        help_text="Product price"
    )
    discount_percentage = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Discount percentage (0-100)"
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0.00)],
        help_text="Fixed discount amount"
    )
    
    # Inventory management
    in_stock = models.BooleanField(
        default=True,
        help_text="Product availability status"
    )
    stock_quantity = models.PositiveIntegerField(
        default=0,
        help_text="Available stock quantity"
    )
    
    # Product media
    image_urls = models.JSONField(
        default=list,
        blank=True,
        help_text="List of product image URLs"
    )
    
    # AI optimization fields
    ai_tags = models.JSONField(
        default=list,
        blank=True,
        help_text="AI-generated tags for enhanced search and recommendations"
    )
    similarity_vector = models.JSONField(
        default=list,
        blank=True,
        help_text="AI-generated similarity vector for recommendations"
    )
    
    # Product metrics for AI analysis
    view_count = models.PositiveIntegerField(
        default=0,
        help_text="Total product views"
    )
    purchase_count = models.PositiveIntegerField(
        default=0,
        help_text="Total purchases"
    )
    average_rating = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(5.0)],
        help_text="Average customer rating (0-5 stars)"
    )
    total_reviews = models.PositiveIntegerField(
        default=0,
        help_text="Total number of reviews"
    )
    
    # Product status and metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Product visibility status"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Featured product status"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'products'
        verbose_name = 'Product'
        verbose_name_plural = 'Products'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['is_featured']),
            models.Index(fields=['in_stock']),
            models.Index(fields=['category']),
            models.Index(fields=['brand']),
            models.Index(fields=['store']),
            models.Index(fields=['price']),
            models.Index(fields=['average_rating']),
            models.Index(fields=['view_count']),
            models.Index(fields=['purchase_count']),
        ]
    
    def save(self, *args, **kwargs):
        """Auto-generate slug and calculate discounted price."""
        if not self.slug:
            base_slug = slugify(self.name)
            slug = base_slug
            counter = 1
            while Product.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.name} - {self.store.name}"
    
    @property
    def discounted_price(self):
        """Calculate final price after discounts."""
        price = self.price
        
        # Apply percentage discount
        if self.discount_percentage > 0:
            price = price * (1 - self.discount_percentage / 100)
        
        # Apply fixed amount discount
        if self.discount_amount > 0:
            price = max(price - self.discount_amount, 0)
        
        return round(price, 2)
    
    @property
    def is_on_sale(self):
        """Check if product has any active discount."""
        return self.discount_percentage > 0 or self.discount_amount > 0
    
    @property
    def primary_image(self):
        """Get primary product image URL."""
        if self.image_urls:
            return self.image_urls[0]
        return None
    
    def increment_view_count(self):
        """Increment product view count for analytics."""
        self.view_count += 1
        self.save(update_fields=['view_count'])
    
    def increment_purchase_count(self):
        """Increment purchase count for analytics."""
        self.purchase_count += 1
        self.save(update_fields=['purchase_count'])


class ProductAttribute(models.Model):
    """
    Product attributes for detailed specifications.
    
    Flexible attribute system for storing product specifications
    that can be used for AI-powered comparisons and filtering.
    """
    
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='attributes',
        help_text="Product this attribute belongs to"
    )
    name = models.CharField(
        max_length=100,
        help_text="Attribute name (e.g., 'Color', 'Size', 'Weight')"
    )
    value = models.CharField(
        max_length=200,
        help_text="Attribute value"
    )
    
    # For AI comparison optimization
    is_comparable = models.BooleanField(
        default=True,
        help_text="Whether this attribute should be used in AI comparisons"
    )
    
    class Meta:
        db_table = 'product_attributes'
        verbose_name = 'Product Attribute'
        verbose_name_plural = 'Product Attributes'
        unique_together = ['product', 'name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_comparable']),
        ]
    
    def __str__(self):
        return f"{self.product.name} - {self.name}: {self.value}"
