from django.shortcuts import render

def home(request):
    from products.models import Category, Product, Store, Brand
    from promotions.models import Promotion
    from comments.models import Comment
    from django.utils import timezone

    # Featured categories (top 6 active, ordered)
    featured_categories = Category.objects.filter(is_active=True, parent=None).order_by('sort_order')[:6]

    # Trending products (top 8 by view_count, in stock, active)
    trending_products = Product.objects.filter(is_active=True, in_stock=True).order_by('-view_count')[:8]

    # AI Recommendations (top 8 featured, fallback to trending)
    ai_recommendations = Product.objects.filter(is_active=True, is_featured=True, in_stock=True)[:8]
    if ai_recommendations.count() < 8:
        ai_recommendations = trending_products

    # Trending stores (top 6 by average_rating, active, verified)
    trending_stores = Store.objects.filter(is_active=True, is_verified=True).order_by('-average_rating')[:6]

    # Active promotions (top 4, currently active)
    now = timezone.now()
    active_promotions = Promotion.objects.filter(status='active', start_date__lte=now, end_date__gte=now).order_by('-created_at')[:4]

    # Recent customer reviews (top 5, approved, top-level)
    customer_reviews = Comment.objects.filter(is_approved=True, parent=None).order_by('-created_at')[:5]

    context = {
        'featured_categories': featured_categories,
        'trending_products': trending_products,
        'ai_recommendations': ai_recommendations,
        'trending_stores': trending_stores,
        'active_promotions': active_promotions,
        'customer_reviews': customer_reviews,
    }
    return render(request, 'core/home.html', context)

def search(request):
    return render(request, 'core/search.html')

def product_details(request, id):
    return render(request, 'core/product_details.html', {'id': id})

def categories(request):
    return render(request, 'core/categories.html')

def brands(request):
    return render(request, 'core/brands.html')

def login_view(request):
    from django.contrib.auth import authenticate, login
    context = {"form": {"errors": {}}}
    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")
        errors = {}
        user = authenticate(request, username=username, password=password)
        if user is not None:
            if not user.is_active:
                errors["username"] = ["This account is inactive."]
            else:
                login(request, user)
                return render(request, "core/login.html", {"success": True})
        else:
            errors["username"] = ["Invalid username or password."]
        context["form"] = {"errors": errors}
        context["username"] = username
    return render(request, 'core/login.html', context)

def register(request):
    from django.contrib.auth import get_user_model
    from django.contrib.auth.password_validation import validate_password
    from django.core.exceptions import ValidationError
    from auth_app.models import UserProfile
    User = get_user_model()
    context = {"form": {"errors": {}}}
    if request.method == "POST":
        role = request.POST.get("role")
        username = request.POST.get("username")
        email = request.POST.get("email")
        password = request.POST.get("password")
        password2 = request.POST.get("password_confirm")
        store_name = request.POST.get("store_name")
        business_license = request.POST.get("business_license")
        errors = {}
        # Email uniqueness
        if User.objects.filter(email=email).exists():
            errors["email"] = ["Email is already registered."]
        # Username uniqueness
        if User.objects.filter(username=username).exists():
            errors["username"] = ["Username is already taken."]
        # Password match
        if password != (password2 or password):
            errors["password"] = ["Passwords do not match."]
        # No password strength validation (accept any password)
        # Store owner required fields
        if role == "store_owner":
            if not store_name:
                errors["store_name"] = ["Store name is required."]
            if not business_license:
                errors["business_license"] = ["Business license is required."]
        if not errors:
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                user_type=role,
                store_name=store_name if role == "store_owner" else "",
                business_license=business_license if role == "store_owner" else ""
            )
            UserProfile.objects.create(user=user)
            # Optionally: send verification email here
            return render(request, "core/register.html", {"success": True})
        context["form"] = {"errors": errors}
    return render(request, 'core/register.html', context)

def cart(request):
    return render(request, 'core/cart.html')

def checkout(request):
    return render(request, 'core/checkout.html')

def recommendations(request):
    return render(request, 'core/recommendations.html')

def compare(request):
    return render(request, 'core/compare.html')

def profile(request):
    return render(request, 'core/profile.html')

def orders(request):
    return render(request, 'core/orders.html')
