from django.contrib import admin
from .models import Store, StoreBranch

@admin.register(Store)
class StoreAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'city', 'country', 'average_rating', 'is_active')
    search_fields = ('name', 'city', 'country', 'owner__username')
    list_filter = ('is_active', 'city', 'country')

@admin.register(StoreBranch)
class StoreBranchAdmin(admin.ModelAdmin):
    list_display = ('store', 'name', 'city', 'address', 'is_active')
    search_fields = ('name', 'city', 'address')
    list_filter = ('is_active',)
