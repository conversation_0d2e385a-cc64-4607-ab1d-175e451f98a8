"""
Serializers for the reports app.
"""

from rest_framework import serializers
from .models import GeneratedReport


class GeneratedReportSerializer(serializers.ModelSerializer):
    """Serializer for the GeneratedReport model."""
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    
    class Meta:
        model = GeneratedReport
        fields = [
            'id', 'report_type', 'report_type_display', 'status',
            'file_url', 'ai_summary_text', 'generated_at', 'completed_at'
        ]


class GenerateReportSerializer(serializers.Serializer):
    """Serializer for validating a report generation request."""
    report_type = serializers.ChoiceField(choices=GeneratedReport.REPORT_TYPE_CHOICES)
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    
    def validate(self, attrs):
        if 'start_date' in attrs and 'end_date' in attrs:
            if attrs['start_date'] > attrs['end_date']:
                raise serializers.ValidationError("Start date cannot be after end date.")
        return attrs
