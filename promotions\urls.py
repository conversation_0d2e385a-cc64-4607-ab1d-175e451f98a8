"""
URL routing for promotion and discount management endpoints.
"""

from django.urls import path
from . import views

app_name = 'promotions'

urlpatterns = [
    # User-facing promotion endpoints
    path('active/', views.UserActivePromotionsView.as_view(), name='user_active_promotions'),
    path('generate-qr/', views.GenerateUserQRView.as_view(), name='generate_user_qr'),
    path('validate-qr/', views.ValidateStoreQRView.as_view(), name='validate_store_qr'),
    path('history/', views.UserQRHistoryView.as_view(), name='user_qr_history'),
    
    # Admin-only promotion management
    path('admin/', views.PromotionAdminListView.as_view(), name='admin_promotion_list'),
    path('admin/<uuid:pk>/', views.PromotionAdminDetailView.as_view(), name='admin_promotion_detail'),
]
