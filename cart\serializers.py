"""
Serializers for the shopping cart.
"""

from rest_framework import serializers
from .models import Cart, CartItem
from products.serializers import ProductListSerializer


class CartItemSerializer(serializers.ModelSerializer):
    """Serializer for items within the cart."""
    product = ProductListSerializer(read_only=True)
    total_price = serializers.ReadOnlyField()
    
    class Meta:
        model = CartItem
        fields = ['id', 'product', 'quantity', 'total_price']


class CartSerializer(serializers.ModelSerializer):
    """Serializer for the main shopping cart."""
    items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.ReadOnlyField()
    total_items = serializers.ReadOnlyField()
    
    class Meta:
        model = Cart
        fields = ['id', 'user', 'session_key', 'items', 'total_price', 'total_items', 'updated_at']


class AddToCartSerializer(serializers.Serializer):
    """Serializer for adding an item to the cart."""
    product_id = serializers.UUIDField()
    quantity = serializers.IntegerField(min_value=1, default=1)
    
    def validate_product_id(self, value):
        """Check if the product exists and is in stock."""
        from products.models import Product
        try:
            product = Product.objects.get(id=value, is_active=True, in_stock=True)
            self.context['product'] = product
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product not found or is out of stock.")
    
    def validate(self, attrs):
        """Check if quantity exceeds stock."""
        product = self.context['product']
        if product.stock_quantity < attrs['quantity']:
            raise serializers.ValidationError(f"Not enough stock for {product.name}. Available: {product.stock_quantity}")
        return attrs
