"""
Main URL configuration for Best on Click e-commerce backend.

This file routes all API endpoints to their respective Django apps,
maintaining a clean and organized URL structure for the React frontend.
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

# API version prefix for future-proofing
API_VERSION = 'v1'

urlpatterns = [
    # Admin interface
    path('admin/', admin.site.urls),
    
    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    
    # Authentication endpoints
    path(f'api/{API_VERSION}/auth/', include('auth_app.urls')),
    
    # Core e-commerce functionality
    path(f'api/{API_VERSION}/products/', include('products.urls')),
    path(f'api/{API_VERSION}/promotions/', include('promotions.urls')),
    path(f'api/{API_VERSION}/cart/', include('cart.urls')),
    path(f'api/{API_VERSION}/comments/', include('comments.urls')),
    
    # AI-powered features
    path(f'api/{API_VERSION}/recommendations/', include('recommendations.urls')),
    path(f'api/{API_VERSION}/comparisons/', include('comparisons.urls')),
    path(f'api/{API_VERSION}/user-behavior/', include('ai_models.urls')),
    
    # Dashboard and analytics
    path(f'api/{API_VERSION}/dashboard/', include('dashboard.urls')),
    path(f'api/{API_VERSION}/reports/', include('reports.urls')),
    # Store Owner dashboard (web)
    path('dashboard/', include('dashboard.urls')),
    # Admin dashboard (web)
    path('admin-panel/', include('admin.urls')),
    # Visitor & Customer pages (home page first)
    path('', include('core.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
