<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقارنة المنتجات | Best IN Click</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2 class="mb-4 text-center">مقارنة المنتجات</h2>
        <form method="get" class="mb-4">
            <div class="row">
                <div class="col-md-5">
                    <select name="product1" class="form-select">
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 text-center">
                    <span class="fw-bold">VS</span>
                </div>
                <div class="col-md-5">
                    <select name="product2" class="form-select">
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <button type="submit" class="btn btn-primary mt-3 w-100">مقارنة</button>
        </form>
        {% if comparison %}
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ comparison.product1.name }}</h5>
                        <p>{{ comparison.product1.description }}</p>
                        <p><strong>السعر:</strong> {{ comparison.product1.price }} ريال</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ comparison.product2.name }}</h5>
                        <p>{{ comparison.product2.description }}</p>
                        <p><strong>السعر:</strong> {{ comparison.product2.price }} ريال</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4">
            <h5>التحليل الذكي:</h5>
            <p>{{ comparison.analysis }}</p>
        </div>
        {% endif %}
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
