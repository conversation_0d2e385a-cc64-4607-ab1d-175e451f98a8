"""
Custom permission classes for role-based access control (RBAC).

These permissions are used throughout the application to ensure that
users can only access the endpoints and perform the actions
appropriate for their assigned role (<PERSON><PERSON>, Store Owner, Customer).
"""

from rest_framework.permissions import BasePermission


class IsAdminUser(BasePermission):
    """
    Allows access only to admin users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_system_admin)


class IsStoreOwner(BasePermission):
    """
    Allows access only to store owner users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_store_owner)


class IsCustomer(BasePermission):
    """
    Allows access only to customer users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_customer)
