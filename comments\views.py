"""
Views for managing product comments, ratings, and replies.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.db.models import Avg
from django.shortcuts import get_object_or_404
import logging

from .models import Comment
from .serializers import CommentSerializer
from products.models import Product
from .tasks import analyze_comment_sentiment_task

logger = logging.getLogger(__name__)


class ProductCommentListView(generics.ListCreateAPIView):
    """
    API endpoint for listing and creating comments for a specific product.
    """
    serializer_class = CommentSerializer
    
    def get_permissions(self):
        """Allow anyone to read, but only authenticated users to create."""
        if self.request.method == 'POST':
            return [permissions.IsAuthenticated()]
        return [permissions.AllowAny()]
    
    def get_queryset(self):
        """Return approved, top-level comments for the given product."""
        product_id = self.kwargs.get('product_id')
        return Comment.objects.filter(
            product_id=product_id,
            is_approved=True,
            parent__isnull=True
        ).select_related('user', 'sentiment_result').prefetch_related('replies__user')
    
    def perform_create(self, serializer):
        """
        Create a new comment and trigger asynchronous sentiment analysis.
        """
        product = get_object_or_404(Product, id=self.kwargs.get('product_id'))
        
        comment = serializer.save(user=self.request.user, product=product)
        
        # Trigger background task for sentiment analysis
        analyze_comment_sentiment_task.delay(comment.id)
        
        # Update product's average rating
        self._update_product_rating(product)
        
        logger.info(f"Comment {comment.id} created by {self.request.user.username} for product {product.name}")
    
    def _update_product_rating(self, product):
        """Recalculate and update the average rating for a product."""
        new_avg = product.comments.filter(is_approved=True, parent__isnull=True).aggregate(
            avg_rating=Avg('rating')
        )['avg_rating']
        
        if new_avg is not None:
            product.average_rating = new_avg
            product.total_reviews = product.comments.filter(is_approved=True, parent__isnull=True).count()
            product.save(update_fields=['average_rating', 'total_reviews'])


class CommentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API endpoint for retrieving, updating, or deleting a specific comment.
    """
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer
    
    class IsOwnerOrAdmin(permissions.BasePermission):
        """Allow access only to the comment owner or an admin."""
        def has_object_permission(self, request, view, obj):
            return obj.user == request.user or request.user.is_system_admin
    
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]
    
    def perform_destroy(self, instance):
        """
        Instead of deleting, mark the comment as not approved.
        This preserves data but hides the comment.
        """
        product = instance.product
        instance.is_approved = False
        instance.save()
        
        # Update product's average rating after "deletion"
        self._update_product_rating(product)
        logger.warning(f"Comment {instance.id} hidden by {self.request.user.username}")
    
    def _update_product_rating(self, product):
        """Recalculate and update the average rating for a product."""
        new_avg = product.comments.filter(is_approved=True, parent__isnull=True).aggregate(
            avg_rating=Avg('rating')
        )['avg_rating']
        
        product.average_rating = new_avg if new_avg is not None else 0
        product.total_reviews = product.comments.filter(is_approved=True, parent__isnull=True).count()
        product.save(update_fields=['average_rating', 'total_reviews'])
