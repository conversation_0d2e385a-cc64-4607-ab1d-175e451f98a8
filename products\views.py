"""
Product management views for Best on Click e-commerce platform.

This module provides comprehensive product API endpoints with
AI-powered features, advanced filtering, and role-based access control.
"""

from rest_framework import generics, status, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Avg, Count
from django.shortcuts import get_object_or_404
import logging

from .models import Product, Category, Brand, Store
from .serializers import (
    ProductListSerializer,
    ProductDetailSerializer,
    ProductCreateUpdateSerializer,
    CategorySerializer,
    BrandSerializer,
    StoreSerializer
)
from ai_models.services import AIRecommendationService, AISearchService

logger = logging.getLogger(__name__)


class ProductListView(generics.ListAPIView):
    """
    Product listing with advanced filtering and AI-powered search.
    
    Supports filtering by category, brand, price range, and store.
    Includes AI-powered smart search and personalized recommendations.
    """
    
    serializer_class = ProductListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'brand', 'store', 'is_featured', 'in_stock']
    search_fields = ['name', 'description', 'short_description']
    ordering_fields = ['price', 'average_rating', 'created_at', 'view_count']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Get filtered and optimized product queryset."""
        queryset = Product.objects.filter(
            is_active=True
        ).select_related(
            'category', 'brand', 'store'
        ).prefetch_related(
            'attributes'
        )
        
        # Apply custom filters
        price_min = self.request.query_params.get('price_min')
        price_max = self.request.query_params.get('price_max')
        
        if price_min:
            queryset = queryset.filter(price__gte=price_min)
        if price_max:
            queryset = queryset.filter(price__lte=price_max)
        
        # AI-powered smart search
        smart_search = self.request.query_params.get('smart_search')
        if smart_search:
            try:
                ai_service = AISearchService()
                enhanced_query = ai_service.enhance_search_query(smart_search)
                queryset = ai_service.apply_smart_search(queryset, enhanced_query)
            except Exception as e:
                logger.error(f"Smart search error: {str(e)}")
                # Fallback to regular search
                queryset = queryset.filter(
                    Q(name__icontains=smart_search) |
                    Q(description__icontains=smart_search)
                )
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """Enhanced list response with AI recommendations."""
        response = super().list(request, *args, **kwargs)
        
        # Add AI-powered general recommendations if user is authenticated
        if request.user.is_authenticated:
            try:
                ai_service = AIRecommendationService()
                recommendations = ai_service.get_personalized_recommendations(
                    request.user, limit=5
                )
                response.data['personalized_recommendations'] = recommendations
            except Exception as e:
                logger.error(f"Recommendation error: {str(e)}")
        
        return response


class ProductDetailView(generics.RetrieveAPIView):
    """
    Product detail view with AI-powered similar products.
    
    Provides comprehensive product information including
    AI-generated similar products and personalized recommendations.
    """
    
    serializer_class = ProductDetailSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'slug'
    
    def get_queryset(self):
        """Get optimized product queryset with relationships."""
        return Product.objects.filter(
            is_active=True
        ).select_related(
            'category', 'brand', 'store', 'store__owner'
        ).prefetch_related(
            'attributes'
        )
    
    def retrieve(self, request, *args, **kwargs):
        """Enhanced retrieve with view tracking and AI features."""
        instance = self.get_object()
        
        # Track product view for analytics and AI
        instance.increment_view_count()
        
        # Log user behavior for AI training
        if hasattr(request, 'user') and request.user.is_authenticated:
            from ai_models.services import UserBehaviorService
            try:
                behavior_service = UserBehaviorService()
                behavior_service.log_interaction(
                    user=request.user,
                    product=instance,
                    action_type='view',
                    metadata={'source': 'product_detail'}
                )
            except Exception as e:
                logger.error(f"Behavior logging error: {str(e)}")
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ProductCreateView(generics.CreateAPIView):
    """
    Product creation endpoint for store owners.
    
    Allows authenticated store owners to create new products
    with comprehensive validation and automatic store assignment.
    """
    
    serializer_class = ProductCreateUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        """Ensure only store owners can create products."""
        permission_classes = [permissions.IsAuthenticated]
        
        # Add custom permission for store owners
        class IsStoreOwner(permissions.BasePermission):
            def has_permission(self, request, view):
                return request.user.is_store_owner
        
        permission_classes.append(IsStoreOwner)
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Create product with automatic store assignment."""
        # Get user's active store
        store = Store.objects.filter(
            owner=self.request.user,
            is_active=True
        ).first()
        
        if not store:
            raise serializers.ValidationError("No active store found.")
        
        serializer.save(store=store)
        logger.info(f"Product created: {serializer.instance.name} by {self.request.user.username}")


class ProductUpdateView(generics.UpdateAPIView):
    """
    Product update endpoint for store owners.
    
    Allows store owners to update their own products
    with proper authorization and validation.
    """
    
    serializer_class = ProductCreateUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'slug'
    
    def get_queryset(self):
        """Ensure users can only update their own store's products."""
        if self.request.user.is_store_owner:
            return Product.objects.filter(
                store__owner=self.request.user,
                store__is_active=True
            )
        return Product.objects.none()
    
    def perform_update(self, serializer):
        """Update product with logging."""
        serializer.save()
        logger.info(f"Product updated: {serializer.instance.name} by {self.request.user.username}")


class ProductDeleteView(generics.DestroyAPIView):
    """
    Product deletion endpoint for store owners.
    
    Allows store owners to delete their own products
    with proper authorization checks.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'slug'
    
    def get_queryset(self):
        """Ensure users can only delete their own store's products."""
        if self.request.user.is_store_owner:
            return Product.objects.filter(
                store__owner=self.request.user,
                store__is_active=True
            )
        return Product.objects.none()
    
    def perform_destroy(self, instance):
        """Soft delete by marking as inactive."""
        instance.is_active = False
        instance.save()
        logger.info(f"Product deleted: {instance.name} by {self.request.user.username}")


class CategoryListView(generics.ListAPIView):
    """
    Category listing with hierarchical structure.
    
    Provides category tree with product counts
    for navigation and filtering purposes.
    """
    
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        """Get active categories with product counts."""
        return Category.objects.filter(
            is_active=True
        ).prefetch_related(
            'subcategories', 'products'
        ).order_by('sort_order', 'name')


class BrandListView(generics.ListAPIView):
    """
    Brand listing with reputation metrics.
    
    Provides brand information with AI-calculated
    reputation scores and product counts.
    """
    
    serializer_class = BrandSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'brand_reputation_score']
    ordering = ['name']
    
    def get_queryset(self):
        """Get active brands with product counts."""
        return Brand.objects.filter(
            is_active=True
        ).prefetch_related('products')


class StoreListView(generics.ListAPIView):
    """
    Store listing with comprehensive metrics.
    
    Provides store information with AI-powered
    analytics and customer service scores.
    """
    
    serializer_class = StoreSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'average_rating', 'customer_service_score']
    ordering = ['-average_rating']
    
    def get_queryset(self):
        """Get active and verified stores."""
        return Store.objects.filter(
            is_active=True,
            is_verified=True
        ).select_related('owner').prefetch_related('products')


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def product_search_suggestions(request):
    """
    AI-powered search suggestions endpoint.
    
    Provides intelligent search suggestions based on
    user input, popular searches, and AI analysis.
    """
    query = request.query_params.get('q', '').strip()
    
    if not query or len(query) < 2:
        return Response({'suggestions': []})
    
    try:
        # Get AI-powered search suggestions
        ai_service = AISearchService()
        suggestions = ai_service.get_search_suggestions(query, limit=10)
        
        return Response({
            'suggestions': suggestions,
            'query': query
        })
        
    except Exception as e:
        logger.error(f"Search suggestions error: {str(e)}")
        
        # Fallback to simple product name matching
        products = Product.objects.filter(
            name__icontains=query,
            is_active=True
        ).values_list('name', flat=True)[:10]
        
        return Response({
            'suggestions': list(products),
            'query': query
        })


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def featured_products(request):
    """
    Get featured products with AI optimization.
    
    Returns curated list of featured products with
    personalized ordering based on user preferences.
    """
    try:
        queryset = Product.objects.filter(
            is_active=True,
            is_featured=True,
            in_stock=True
        ).select_related('category', 'brand', 'store')
        
        # Apply AI-powered personalization if user is authenticated
        if request.user.is_authenticated:
            ai_service = AIRecommendationService()
            queryset = ai_service.personalize_product_list(request.user, queryset)
        
        # Limit to top 20 featured products
        products = queryset[:20]
        serializer = ProductListSerializer(products, many=True, context={'request': request})
        
        return Response({
            'featured_products': serializer.data,
            'count': len(serializer.data)
        })
        
    except Exception as e:
        logger.error(f"Featured products error: {str(e)}")
        return Response({
            'error': 'Failed to load featured products.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def toggle_product_stock(request, slug):
    """
    Toggle product stock status for store owners.
    
    Allows store owners to quickly toggle in_stock status
    for inventory management purposes.
    """
    try:
        product = get_object_or_404(
            Product,
            slug=slug,
            store__owner=request.user,
            store__is_active=True
        )
        
        product.in_stock = not product.in_stock
        product.save(update_fields=['in_stock'])
        
        logger.info(f"Stock toggled for {product.name}: {product.in_stock}")
        
        return Response({
            'message': f'Product stock status updated to {"In Stock" if product.in_stock else "Out of Stock"}',
            'in_stock': product.in_stock
        })
        
    except Product.DoesNotExist:
        return Response({
            'error': 'Product not found or access denied.'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Stock toggle error: {str(e)}")
        return Response({
            'error': 'Failed to update stock status.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
