"""
API endpoints for AI-powered product and store comparisons.
"""

from rest_framework import views, response, status, permissions
from products.models import Product, Store
from products.serializers import ProductDetailSerializer, StoreSerializer
import logging

logger = logging.getLogger(__name__)


class ProductComparisonView(views.APIView):
    """
    Compares a list of products and returns a structured comparison.
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request, *args, **kwargs):
        product_ids = request.data.get('product_ids', [])
        
        if not isinstance(product_ids, list) or len(product_ids) < 2:
            return response.Response(
                {"error": "Please provide a list of at least two product IDs to compare."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        products = Product.objects.filter(id__in=product_ids, is_active=True).prefetch_related('attributes')
        
        if products.count() < 2:
            return response.Response(
                {"error": "Could not find at least two valid products for comparison."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Conceptual: AI service would generate this comparison data
        comparison_data = self._generate_comparison_data(products)
        
        return response.Response(comparison_data)
    
    def _generate_comparison_data(self, products):
        """
        Conceptual: Generates a structured comparison.
        A real implementation would use an AI model to extract key features,
        find common attributes, and highlight strengths/weaknesses.
        """
        product_data = ProductDetailSerializer(products, many=True).data
        
        # Gather all unique attribute names
        all_attributes = set()
        for p in products:
            for attr in p.attributes.filter(is_comparable=True):
                all_attributes.add(attr.name)
        
        # Build comparison table
        comparison_table = {}
        for attr_name in sorted(list(all_attributes)):
            comparison_table[attr_name] = {
                str(p.id): p.attributes.filter(name=attr_name).first().value
                if p.attributes.filter(name=attr_name).exists() else '-'
                for p in products
            }
        
        # AI-generated summary (placeholder)
        ai_summary = f"Comparing {len(products)} products. {products[0].name} is highly rated, while {products[1].name} offers a lower price."
        
        return {
            "products": product_data,
            "comparison_table": comparison_table,
            "ai_summary": ai_summary
        }


class StoreComparisonView(views.APIView):
    """
    Compares a list of stores based on AI-driven metrics.
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request, *args, **kwargs):
        store_ids = request.data.get('store_ids', [])
        
        if not isinstance(store_ids, list) or len(store_ids) < 2:
            return response.Response(
                {"error": "Please provide a list of at least two store IDs to compare."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        stores = Store.objects.filter(id__in=store_ids, is_active=True, is_verified=True)
        
        if stores.count() < 2:
            return response.Response(
                {"error": "Could not find at least two valid stores for comparison."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Conceptual: AI service would generate this
        comparison_data = self._generate_store_comparison(stores)
        
        return response.Response(comparison_data)
    
    def _generate_store_comparison(self, stores):
        """Conceptual: Generates a structured store comparison."""
        store_data = StoreSerializer(stores, many=True).data
        
        # AI-generated summary (placeholder)
        ai_summary = f"{stores[0].name} has a higher customer service score, but {stores[1].name} has more products available."
        
        return {
            "stores": store_data,
            "ai_summary": ai_summary
        }
