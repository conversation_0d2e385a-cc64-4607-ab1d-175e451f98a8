"""
Celery tasks for generating comprehensive reports asynchronously.
"""

from celery import shared_task
from django.utils import timezone
import logging
import pandas as pd
from io import StringIO

from .models import GeneratedReport
from products.models import Product, Store

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def generate_comprehensive_report_task(self, report_id):
    """
    A Celery task to generate a report, save it as a CSV,
    and generate an AI-powered narrative summary.
    """
    try:
        report = GeneratedReport.objects.get(id=report_id)
        report.status = 'processing'
        report.save()
        
        logger.info(f"Starting report generation for report ID: {report_id}")
        
        # --- 1. Data Aggregation (Conceptual) ---
        # This is where you would perform your complex database queries.
        # For this example, we'll use some placeholder logic.
        if report.report_type == 'product_performance':
            data = Product.objects.filter(store=report.store).values(
                'name', 'price', 'purchase_count', 'view_count', 'average_rating'
            )
            df = pd.DataFrame(list(data))
        else: # Default to a simple sales summary
            # In a real app, this would query an Order model
            df = pd.DataFrame([
                {'month': 'Jan', 'sales': 1200},
                {'month': 'Feb', 'sales': 1800},
                {'month': 'Mar', 'sales': 1500},
            ])
        
        # --- 2. File Generation (e.g., CSV) ---
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index=False)
        csv_content = csv_buffer.getvalue()
        
        # --- 3. Upload to Storage (Conceptual) ---
        # In a real app, you would upload this to S3, GCS, or Vercel Blob.
        # For now, we'll just store a placeholder URL.
        # from my_storage_utils import upload_file
        # file_url = upload_file(f"reports/{report.id}.csv", csv_content)
        file_url = f"https://example.com/reports/{report.id}.csv"
        
        # --- 4. AI Data Storytelling (Conceptual) ---
        # This would call a service that uses an LLM to summarize the data.
        # from ai_models.services import DataStorytellingService
        # storyteller = DataStorytellingService()
        # ai_summary = storyteller.summarize_dataframe(df)
        ai_summary = f"This report, generated on {timezone.now().strftime('%Y-%m-%d')}, shows key trends. The top performing product was '{df.iloc[0]['name']}' with {df.iloc[0]['purchase_count']} sales." if not df.empty else "No data available for this period."
        
        # --- 5. Update Report Model ---
        report.status = 'completed'
        report.file_url = file_url
        report.ai_summary_text = ai_summary
        report.completed_at = timezone.now()
        report.save()
        
        logger.info(f"Report generation completed for report ID: {report_id}")
        return f"Report {report_id} completed successfully."
        
    except GeneratedReport.DoesNotExist:
        logger.error(f"Report with ID {report_id} not found.")
    except Exception as e:
        logger.error(f"Failed to generate report {report_id}: {e}")
        try:
            report = GeneratedReport.objects.get(id=report_id)
            report.status = 'failed'
            report.ai_summary_text = f"Report generation failed with error: {e}"
            report.save()
        except GeneratedReport.DoesNotExist:
            pass
        # Do not retry automatically, let user re-trigger
