{% extends 'core/base.html' %}

{% block title %}Manage Products{% endblock %}

{% block content %}
<div class="container mt-5">
  <h1 class="mb-4">Manage Products</h1>

  <p>Here you can view, edit, and delete products available on the platform.</p>

  <!-- Example table layout -->
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>Product Name</th>
          <th>Category</th>
          <th>Price</th>
          <th>In Stock</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Example static row -->
        <tr>
          <td>1</td>
          <td>Example Product</td>
          <td>Electronics</td>
          <td>$99.99</td>
          <td>25</td>
          <td>
            <a href="#" class="btn btn-sm btn-primary">Edit</a>
            <a href="#" class="btn btn-sm btn-danger">Delete</a>
          </td>
        </tr>
        <!-- Dynamic rows will be rendered with Django context later -->
      </tbody>
    </table>
  </div>
</div>
{% endblock %}
