"""
Views for managing the shopping cart.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action

from .models import Cart, CartItem
from .serializers import CartSerializer, AddToCartSerializer
from products.models import Product


class CartViewSet(viewsets.GenericViewSet):
    """
    A ViewSet for viewing and editing cart instances.
    """
    serializer_class = CartSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_cart(self, request):
        """Get or create a cart for the current user or session."""
        if request.user.is_authenticated:
            cart, _ = Cart.objects.get_or_create(user=request.user)
        else:
            session_key = request.session.session_key
            if not session_key:
                request.session.create()
                session_key = request.session.session_key
            cart, _ = Cart.objects.get_or_create(session_key=session_key, user=None)
        return cart
    
    @action(detail=False, methods=['get'], url_path='my-cart')
    def my_cart(self, request):
        """Retrieve the current user's cart."""
        cart = self.get_cart(request)
        serializer = self.get_serializer(cart)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'], url_path='add-item')
    def add_item(self, request):
        """Add an item to the cart."""
        cart = self.get_cart(request)
        serializer = AddToCartSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        
        product = serializer.context['product']
        quantity = serializer.validated_data['quantity']
        
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product=product,
            defaults={'quantity': quantity}
        )
        
        if not created:
            cart_item.quantity += quantity
            cart_item.save()
        
        return Response(self.get_serializer(cart).data, status=status.HTTP_200_OK)
    
    @action(detail=False, methods=['put'], url_path='update-item/(?P<item_id>[^/.]+)')
    def update_item(self, request, item_id=None):
        """Update the quantity of an item in the cart."""
        cart = self.get_cart(request)
        quantity = request.data.get('quantity')
        
        if not quantity or int(quantity) <= 0:
            return Response({"error": "A valid quantity is required."}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            item = CartItem.objects.get(id=item_id, cart=cart)
            item.quantity = int(quantity)
            item.save()
            return Response(self.get_serializer(cart).data)
        except CartItem.DoesNotExist:
            return Response({"error": "Item not found in cart."}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['delete'], url_path='remove-item/(?P<item_id>[^/.]+)')
    def remove_item(self, request, item_id=None):
        """Remove an item from the cart."""
        cart = self.get_cart(request)
        try:
            item = CartItem.objects.get(id=item_id, cart=cart)
            item.delete()
            return Response(self.get_serializer(cart).data)
        except CartItem.DoesNotExist:
            return Response({"error": "Item not found in cart."}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['delete'], url_path='clear')
    def clear_cart(self, request):
        """Clear all items from the cart."""
        cart = self.get_cart(request)
        cart.items.all().delete()
        return Response(self.get_serializer(cart).data)
