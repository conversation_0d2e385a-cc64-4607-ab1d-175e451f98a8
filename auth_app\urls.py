"""
URL routing for authentication endpoints.

Defines all authentication-related API endpoints including
registration, login, profile management, and JWT token handling.
"""

from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'auth_app'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.CustomTokenObtainPairView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # Profile management
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('account/', views.UserAccountView.as_view(), name='account'),
    path('password/change/', views.PasswordChangeView.as_view(), name='password_change'),
    
    # Dashboard
    path('dashboard/stats/', views.user_dashboard_stats, name='dashboard_stats'),
]
