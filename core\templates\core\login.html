{% extends 'core/base.html' %}
{% block title %}Login | Best in Click{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center min-vh-100">
  <div class="col-md-5">
    <div class="card shadow-lg border-0">
      <div class="card-body p-5">
        <div class="text-center mb-4">
          <img src="/static/logo.png" alt="Best in Click" style="height: 60px;">
          <h2 class="mt-2 mb-0" style="font-weight: bold; color: #0d6efd;">Best in Click</h2>
          <p class="text-muted">Welcome back! Please login to your account.</p>
        </div>

        {% if form.errors %}
          <div class="alert alert-danger">
            {% for field in form %}
              {% for error in field.errors %}
                <div>{{ error }}</div>
              {% endfor %}
            {% endfor %}
            {% for error in form.non_field_errors %}
              <div>{{ error }}</div>
            {% endfor %}
          </div>
        {% endif %}

        {% if success %}
          <div class="alert alert-success">Login successful! Welcome back.</div>
        {% else %}
        <form method="post" action="">
          {% csrf_token %}
          {{ form.hidden_fields }}
          <div class="mb-3">
            <label for="username" class="form-label">Username or Email</label>
            <input type="text" class="form-control {% if form.errors.username %}is-invalid{% endif %}" id="username" name="username" value="{{ form.username.value|default:'' }}" required autofocus>
            {% if form.errors.username %}
              <div class="invalid-feedback">{{ form.errors.username.0 }}</div>
            {% endif %}
          </div>

          <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>

          <button type="submit" class="btn btn-primary w-100">Login</button>
        </form>
        {% endif %}

        <div class="mt-4 text-center">
          <span class="text-muted">Don't have an account?</span>
          <a href="/register/" class="ms-1">Register now</a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
