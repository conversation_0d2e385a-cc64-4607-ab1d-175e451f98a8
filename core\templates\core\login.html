{% extends 'core/base.html' %}
{% block title %}Login | Best in Click{% endblock %}

{% block content %}
<style>
  .login-container {
    min-height: 80vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2rem;
    margin: 2rem 0;
  }
  .login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 1.5rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .btn-primary {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem;
    font-weight: 600;
    transition: all 0.3s;
  }
  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
  }
  .social-login {
    border: 1px solid #dee2e6;
    border-radius: 0.75rem;
    padding: 0.75rem;
    transition: all 0.3s;
  }
  .social-login:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
  }
</style>

<div class="container-fluid login-container d-flex align-items-center justify-content-center">
  <div class="row w-100 justify-content-center">
    <div class="col-md-5 col-lg-4">
      <div class="card login-card">
        <div class="card-body p-5">
          <div class="text-center mb-4">
            <div class="mb-3">
              <i class="fas fa-shopping-bag fa-3x text-primary"></i>
            </div>
            <h2 class="fw-bold text-primary mb-2">Welcome Back!</h2>
            <p class="text-muted">Sign in to your Best in Click account</p>
          </div>

          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}

          {% if form.errors %}
            <div class="alert alert-danger alert-dismissible fade show">
              <i class="fas fa-exclamation-triangle me-2"></i>
              {% for field in form %}
                {% for error in field.errors %}
                  <div>{{ error }}</div>
                {% endfor %}
              {% endfor %}
              {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
              {% endfor %}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endif %}

          {% if not success %}
          <form method="post" action="" id="loginForm">
            {% csrf_token %}
            {{ form.hidden_fields }}

            <div class="mb-3">
              <label for="username" class="form-label fw-semibold">
                <i class="fas fa-user me-2"></i>Username or Email
              </label>
              <input type="text"
                     class="form-control form-control-lg {% if form.errors.username %}is-invalid{% endif %}"
                     id="username"
                     name="username"
                     value="{{ form.username.value|default:'' }}"
                     placeholder="Enter your username or email"
                     required
                     autofocus>
              {% if form.errors.username %}
                <div class="invalid-feedback">{{ form.errors.username.0 }}</div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="password" class="form-label fw-semibold">
                <i class="fas fa-lock me-2"></i>Password
              </label>
              <div class="input-group">
                <input type="password"
                       class="form-control form-control-lg {% if form.errors.password %}is-invalid{% endif %}"
                       id="password"
                       name="password"
                       placeholder="Enter your password"
                       required>
                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              {% if form.errors.password %}
                <div class="invalid-feedback">{{ form.errors.password.0 }}</div>
              {% endif %}
            </div>

            <div class="mb-3 d-flex justify-content-between align-items-center">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                <label class="form-check-label" for="rememberMe">
                  Remember me
                </label>
              </div>
              <a href="/password-reset/" class="text-decoration-none">Forgot password?</a>
            </div>

            <button type="submit" class="btn btn-primary w-100 mb-3">
              <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </button>
          </form>

          <!-- Social Login Options -->
          <div class="text-center mb-3">
            <small class="text-muted">Or continue with</small>
          </div>

          <div class="row g-2 mb-4">
            <div class="col-6">
              <a href="#" class="btn social-login w-100 text-decoration-none">
                <i class="fab fa-google text-danger me-2"></i>Google
              </a>
            </div>
            <div class="col-6">
              <a href="#" class="btn social-login w-100 text-decoration-none">
                <i class="fab fa-facebook text-primary me-2"></i>Facebook
              </a>
            </div>
          </div>
          {% endif %}

          <div class="text-center">
            <span class="text-muted">Don't have an account?</span>
            <a href="/register/" class="text-decoration-none fw-semibold">Create Account</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    if (!username || !password) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
});
</script>
{% endblock %}
