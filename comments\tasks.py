"""
Celery tasks for the comments app, such as sentiment analysis.
"""

from celery import shared_task
import logging

from .models import Comment
from ai_models.services import SentimentAnalysisService
from ai_models.models import AISentimentAnalysisResult

logger = logging.getLogger(__name__)


@shared_task
def analyze_comment_sentiment_task(comment_id):
    """
    Asynchronously analyzes the sentiment of a given comment and saves the result.
    """
    try:
        comment = Comment.objects.get(id=comment_id)
        logger.info(f"Starting sentiment analysis for comment {comment_id}")
        
        service = SentimentAnalysisService()
        result = service.analyze_comment(comment.text)
        
        # Update or create the sentiment analysis result
        AISentimentAnalysisResult.objects.update_or_create(
            comment=comment,
            defaults={
                'sentiment': result['sentiment'],
                'confidence_score': result['confidence_score']
            }
        )
        
        logger.info(f"Sentiment analysis complete for comment {comment_id}: {result['sentiment']}")
        return f"Success: {result['sentiment']}"
    except Comment.DoesNotExist:
        logger.error(f"Comment with id {comment_id} not found for sentiment analysis.")
        return "Error: Comment not found"
    except Exception as e:
        logger.error(f"Error during sentiment analysis for comment {comment_id}: {e}")
        # Retry the task if it fails
        raise analyze_comment_sentiment_task.retry(exc=e, countdown=60)
