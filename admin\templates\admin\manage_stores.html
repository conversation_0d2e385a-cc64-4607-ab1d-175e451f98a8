{% extends 'core/base.html' %}

{% block title %}Manage Stores{% endblock %}

{% block content %}
<div class="container mt-5">
  <h1 class="mb-4">Manage Stores</h1>

  <p>Here you can view, approve, or remove registered stores from the platform.</p>

  <!-- Stores Table -->
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>Store Name</th>
          <th>Owner</th>
          <th>City</th>
          <th>Country</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Example static row -->
        <tr>
          <td>1</td>
          <td>Example Store</td>
          <td>John <PERSON></td>
          <td>New York</td>
          <td>USA</td>
          <td><span class="badge bg-success">Approved</span></td>
          <td>
            <a href="#" class="btn btn-sm btn-primary">Edit</a>
            <a href="#" class="btn btn-sm btn-danger">Delete</a>
          </td>
        </tr>
        <!-- You can later loop over dynamic store objects -->
      </tbody>
    </table>
  </div>
</div>
{% endblock %}
