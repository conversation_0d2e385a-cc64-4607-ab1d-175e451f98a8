"""
Serializers for user authentication and profile management.

These serializers handle user registration, login, profile updates,
and JWT token management with comprehensive validation.
"""

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, UserProfile


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration with comprehensive validation.
    
    Handles password confirmation, user type validation, and
    automatic profile creation for new users.
    """
    
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password],
        help_text="Password must meet Django's security requirements"
    )
    password_confirm = serializers.CharField(
        write_only=True,
        help_text="Must match the password field"
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'user_type', 'phone_number',
            'date_of_birth', 'ai_consent', 'store_name', 'business_license'
        ]
        extra_kwargs = {
            'email': {'required': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
        }
    
    def validate(self, attrs):
        """Validate password confirmation and user type specific fields."""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords do not match.")
        
        # Validate store owner specific fields
        if attrs.get('user_type') == 'store_owner':
            if not attrs.get('store_name'):
                raise serializers.ValidationError(
                    "Store name is required for store owners."
                )
            if not attrs.get('business_license'):
                raise serializers.ValidationError(
                    "Business license is required for store owners."
                )
        
        return attrs
    
    def create(self, validated_data):
        """Create user and associated profile."""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(
            password=password,
            **validated_data
        )
        
        # Create associated profile
        UserProfile.objects.create(user=user)
        
        return user


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom JWT token serializer with additional user information.
    
    Includes user type, verification status, and basic profile data
    in the token response for frontend state management.
    """
    
    @classmethod
    def get_token(cls, user):
        """Add custom claims to JWT token."""
        token = super().get_token(user)
        
        # Add custom claims
        token['user_type'] = user.user_type
        token['is_verified'] = user.is_verified
        token['username'] = user.username
        token['full_name'] = user.get_full_display_name()
        
        return token
    
    def validate(self, attrs):
        """Add user information to token response."""
        data = super().validate(attrs)
        
        # Add user information to response
        data['user'] = {
            'id': str(self.user.id),
            'username': self.user.username,
            'email': self.user.email,
            'user_type': self.user.user_type,
            'is_verified': self.user.is_verified,
            'full_name': self.user.get_full_display_name(),
        }
        
        return data


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile management.
    
    Handles profile updates, address management, and preference settings
    for AI personalization and notifications.
    """
    
    user_info = serializers.SerializerMethodField()
    
    class Meta:
        model = UserProfile
        fields = [
            'user_info', 'address_line_1', 'address_line_2',
            'city', 'state', 'postal_code', 'country',
            'budget_range_min', 'budget_range_max',
            'email_notifications', 'sms_notifications',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_user_info(self, obj):
        """Return basic user information."""
        return {
            'id': str(obj.user.id),
            'username': obj.user.username,
            'email': obj.user.email,
            'full_name': obj.user.get_full_display_name(),
            'user_type': obj.user.user_type,
            'phone_number': obj.user.phone_number,
            'ai_consent': obj.user.ai_consent,
        }


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user account information.
    
    Allows users to update their basic information while
    maintaining security constraints on sensitive fields.
    """
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone_number',
            'date_of_birth', 'preferred_categories', 'ai_consent'
        ]
    
    def validate_email(self, value):
        """Ensure email uniqueness excluding current user."""
        if User.objects.exclude(pk=self.instance.pk).filter(email=value).exists():
            raise serializers.ValidationError("This email is already in use.")
        return value


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change functionality.
    
    Validates current password and ensures new password
    meets security requirements.
    """
    
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(
        required=True,
        validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(required=True)
    
    def validate_current_password(self, value):
        """Validate current password."""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Current password is incorrect.")
        return value
    
    def validate(self, attrs):
        """Validate password confirmation."""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords do not match.")
        return attrs
    
    def save(self):
        """Update user password."""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user
