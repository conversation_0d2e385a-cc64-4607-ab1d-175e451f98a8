"""
Models for tracking data used by AI systems.

This module defines models for logging user behavior, tracking real-time
session interactions, and storing sentiment analysis results, which are
all crucial for training and operating the AI features.
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class UserBehaviorLog(models.Model):
    """
    Logs detailed user interactions for AI model training.
    
    This model captures a wide range of user actions, providing the
    foundational data for training recommendation engines, personalization
    algorithms, and other predictive models.
    """
    
    ACTION_TYPES = [
        ('view', 'Product View'),
        ('click', 'Product Click'),
        ('add_to_cart', 'Add to Cart'),
        ('remove_from_cart', 'Remove from Cart'),
        ('purchase', 'Purchase'),
        ('like', 'Like'),
        ('dislike', 'Dislike'),
        ('search', 'Search Query'),
        ('time_on_page', 'Time on Page'),
        ('comparison', 'Product Comparison'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='behavior_logs',
        help_text="The user performing the action (nullable for guests)"
    )
    session_id = models.CharField(
        max_length=255,
        db_index=True,
        help_text="Unique identifier for the user's session"
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='behavior_logs',
        help_text="The product associated with the action"
    )
    action_type = models.CharField(
        max_length=20,
        choices=ACTION_TYPES,
        db_index=True,
        help_text="The type of interaction"
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        db_index=True,
        help_text="The exact time of the interaction"
    )
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional data (e.g., search query, time in seconds, compared products)"
    )
    
    class Meta:
        db_table = 'ai_user_behavior_logs'
        verbose_name = 'User Behavior Log'
        verbose_name_plural = 'User Behavior Logs'
        ordering = ['-timestamp']
    
    def __str__(self):
        user_str = self.user.username if self.user else 'Guest'
        product_str = f" - {self.product.name}" if self.product else ""
        return f"{self.get_action_type_display()} by {user_str}{product_str} at {self.timestamp}"


class UserSessionInteraction(models.Model):
    """
    Tracks real-time session behavior for immediate personalization.
    
    This model is optimized for fast reads and writes to support
    low-latency, in-session recommendations like "recently viewed" or
    "frequently bought together with items in your current cart".
    """
    
    INTERACTION_TYPES = [
        ('view', 'Product View'),
        ('click', 'Product Click'),
        ('add_to_cart', 'Add to Cart'),
    ]
    
    session_id = models.CharField(
        max_length=255,
        primary_key=True,
        help_text="Unique identifier for the user's session"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='session_interactions'
    )
    # Using a JSONField to store a list of interactions is more efficient for this use case
    interactions = models.JSONField(
        default=list,
        help_text="List of interaction events within the session"
    )
    last_updated = models.DateTimeField(
        auto_now=True,
        help_text="Timestamp of the last interaction in this session"
    )
    
    class Meta:
        db_table = 'ai_user_session_interactions'
        verbose_name = 'User Session Interaction'
        verbose_name_plural = 'User Session Interactions'
        ordering = ['-last_updated']
    
    def __str__(self):
        user_str = self.user.username if self.user else 'Guest'
        return f"Session {self.session_id} for {user_str}"


class AISentimentAnalysisResult(models.Model):
    """
    Stores the sentiment analysis result for a product comment.
    
    This decouples the sentiment analysis process from the comment model,
    allowing for asynchronous analysis and re-analysis without altering
    the original comment data.
    """
    
    SENTIMENT_CHOICES = [
        ('positive', 'Positive'),
        ('negative', 'Negative'),
        ('neutral', 'Neutral'),
    ]
    
    comment = models.OneToOneField(
        'comments.Comment',
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='sentiment_result'
    )
    sentiment = models.CharField(
        max_length=10,
        choices=SENTIMENT_CHOICES,
        db_index=True,
        help_text="The classified sentiment of the comment"
    )
    confidence_score = models.FloatField(
        help_text="The model's confidence in the sentiment classification (0.0 to 1.0)"
    )
    analyzed_at = models.DateTimeField(
        auto_now=True,
        help_text="When the analysis was last performed"
    )
    
    class Meta:
        db_table = 'ai_sentiment_analysis_results'
        verbose_name = 'AI Sentiment Analysis Result'
        verbose_name_plural = 'AI Sentiment Analysis Results'
    
    def __str__(self):
        return f"{self.get_sentiment_display()} ({self.confidence_score:.2f}) for Comment {self.comment.id}"
