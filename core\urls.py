from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('search/', views.search, name='search'),
    path('product/<uuid:id>/', views.product_details, name='product-details'),
    path('categories/', views.categories, name='categories'),
    path('brands/', views.brands, name='brands'),
    path('login/', views.login_view, name='login'),
    path('register/', views.register, name='register'),
    path('cart/', views.cart, name='cart'),
    path('checkout/', views.checkout, name='checkout'),
    path('recommendations/', views.recommendations, name='recommendations'),
    path('compare/', views.compare, name='compare'),
    path('profile/', views.profile, name='profile'),
    path('orders/', views.orders, name='orders'),
]
