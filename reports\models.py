"""
Models for tracking the generation of asynchronous reports.
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class GeneratedReport(models.Model):
    """
    Tracks the status and metadata of a generated report.
    
    This allows the system to handle long-running report generation
    tasks asynchronously, while providing the user with status updates
    and a link to the final report file.
    """
    
    REPORT_TYPE_CHOICES = [
        ('sales_summary', 'Sales Summary'),
        ('product_performance', 'Product Performance'),
        ('customer_analytics', 'Customer Analytics'),
        ('sentiment_analysis', 'Sentiment Analysis'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report_type = models.CharField(max_length=50, choices=REPORT_TYPE_CHOICES)
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='generated_reports')
    
    # For store-specific reports
    store = models.ForeignKey(
        'products.Store',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='generated_reports'
    )
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Link to the generated file (e.g., in S3 or another blob storage)
    file_url = models.URLField(max_length=512, blank=True, null=True)
    
    # AI-generated summary of the report's key findings
    ai_summary_text = models.TextField(
        blank=True,
        null=True,
        help_text="AI-generated narrative summary of the report."
    )
    
    # Timestamps
    generated_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'generated_reports'
        verbose_name = 'Generated Report'
        verbose_name_plural = 'Generated Reports'
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.get_report_type_display()} report for {self.generated_by.username}"
