{% extends 'core/base.html' %}
{% block title %}Categories - Best in Click{% endblock %}
{% block content %}
<style>
  .category-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: none;
    border-radius: 1rem;
    overflow: hidden;
  }
  .category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }
  .category-icon {
    font-size: 3rem;
    color: #0d6efd;
    margin-bottom: 1rem;
  }
  .category-stats {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin-top: 1rem;
  }
  .breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 2rem;
  }
  .filter-section {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
</style>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item active" aria-current="page">Categories</li>
  </ol>
</nav>

<!-- Page Header -->
<div class="row mb-4">
  <div class="col-md-8">
    <h1 class="display-5 fw-bold text-primary">Browse Categories</h1>
    <p class="lead text-muted">Discover products organized by categories to find exactly what you're looking for.</p>
  </div>
  <div class="col-md-4 text-end">
    <div class="d-flex justify-content-end align-items-center">
      <label for="sortSelect" class="form-label me-2 mb-0">Sort by:</label>
      <select class="form-select" id="sortSelect" style="width: auto;">
        <option value="name">Name</option>
        <option value="products">Most Products</option>
        <option value="popular">Most Popular</option>
      </select>
    </div>
  </div>
</div>

<!-- Search and Filter -->
<div class="filter-section">
  <div class="row">
    <div class="col-md-6">
      <div class="input-group">
        <input type="text" class="form-control" placeholder="Search categories..." id="categorySearch">
        <button class="btn btn-outline-primary" type="button">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>
    <div class="col-md-6">
      <div class="d-flex gap-2 flex-wrap">
        <button class="btn btn-outline-primary btn-sm active" data-filter="all">All</button>
        <button class="btn btn-outline-primary btn-sm" data-filter="electronics">Electronics</button>
        <button class="btn btn-outline-primary btn-sm" data-filter="fashion">Fashion</button>
        <button class="btn btn-outline-primary btn-sm" data-filter="home">Home & Garden</button>
        <button class="btn btn-outline-primary btn-sm" data-filter="sports">Sports</button>
      </div>
    </div>
  </div>
</div>

<!-- Categories Grid -->
<div class="row" id="categoriesGrid">
  {% for category in categories %}
    <div class="col-lg-3 col-md-4 col-sm-6 mb-4 category-item" data-category="{{ category.slug }}">
      <div class="card category-card h-100">
        <div class="card-body text-center">
          <div class="category-icon">
            <i class="{{ category.icon|default:'fas fa-tag' }}"></i>
          </div>
          <h5 class="card-title">{{ category.name }}</h5>
          <p class="card-text text-muted">{{ category.description|truncatechars:80 }}</p>

          <div class="category-stats">
            <div class="row text-center">
              <div class="col-6">
                <small class="text-muted d-block">Products</small>
                <strong>{{ category.product_count|default:0 }}</strong>
              </div>
              <div class="col-6">
                <small class="text-muted d-block">Stores</small>
                <strong>{{ category.store_count|default:0 }}</strong>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer bg-transparent border-0 text-center">
          <a href="/categories/{{ category.slug }}/" class="btn btn-primary btn-sm">
            <i class="fas fa-arrow-right me-1"></i>Explore
          </a>
        </div>
      </div>
    </div>
  {% empty %}
    <div class="col-12 text-center py-5">
      <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
      <h4 class="text-muted">No categories found</h4>
      <p class="text-muted">Categories will appear here once they are added.</p>
    </div>
  {% endfor %}
</div>

<!-- Popular Categories Section -->
{% if popular_categories %}
<div class="mt-5">
  <h3 class="mb-4">🔥 Most Popular Categories</h3>
  <div class="row">
    {% for category in popular_categories %}
      <div class="col-md-2 col-6 mb-3">
        <div class="card border-0 bg-light text-center p-3">
          <i class="{{ category.icon|default:'fas fa-star' }} fa-2x text-warning mb-2"></i>
          <h6 class="card-title mb-1">{{ category.name }}</h6>
          <small class="text-muted">{{ category.product_count }} products</small>
        </div>
      </div>
    {% endfor %}
  </div>
</div>
{% endif %}

<script>
// Category search functionality
document.getElementById('categorySearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const categoryItems = document.querySelectorAll('.category-item');

    categoryItems.forEach(item => {
        const categoryName = item.querySelector('.card-title').textContent.toLowerCase();
        const categoryDesc = item.querySelector('.card-text').textContent.toLowerCase();

        if (categoryName.includes(searchTerm) || categoryDesc.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Filter buttons functionality
document.querySelectorAll('[data-filter]').forEach(button => {
    button.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('[data-filter]').forEach(btn => btn.classList.remove('active'));
        // Add active class to clicked button
        this.classList.add('active');

        const filter = this.getAttribute('data-filter');
        const categoryItems = document.querySelectorAll('.category-item');

        categoryItems.forEach(item => {
            if (filter === 'all' || item.getAttribute('data-category').includes(filter)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
});

// Sort functionality
document.getElementById('sortSelect').addEventListener('change', function() {
    const sortBy = this.value;
    const grid = document.getElementById('categoriesGrid');
    const items = Array.from(grid.querySelectorAll('.category-item'));

    items.sort((a, b) => {
        if (sortBy === 'name') {
            const nameA = a.querySelector('.card-title').textContent;
            const nameB = b.querySelector('.card-title').textContent;
            return nameA.localeCompare(nameB);
        } else if (sortBy === 'products') {
            const countA = parseInt(a.querySelector('.category-stats strong').textContent);
            const countB = parseInt(b.querySelector('.category-stats strong').textContent);
            return countB - countA;
        }
        return 0;
    });

    // Re-append sorted items
    items.forEach(item => grid.appendChild(item));
});
</script>
{% endblock %}
