<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المنتجات | Best IN Click</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h2 class="mb-4 text-center">قائمة المنتجات</h2>
        <form class="mb-4" method="get">
            <div class="input-group">
                <input type="text" class="form-control" name="search" placeholder="ابحث عن منتج...">
                <button class="btn btn-primary" type="submit">بحث</button>
            </div>
        </form>
        <div class="row">
            {% for product in products %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <img src="{{ product.image_url }}" class="card-img-top" alt="{{ product.name }}">
                    <div class="card-body">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text">{{ product.description|truncatewords:20 }}</p>
                        <p class="card-text"><strong>السعر:</strong> {{ product.price }} ريال</p>
                        <a href="/products/{{ product.id }}/" class="btn btn-outline-primary w-100">تفاصيل المنتج</a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p>لا توجد منتجات متاحة حالياً.</p>
            </div>
            {% endfor %}
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
