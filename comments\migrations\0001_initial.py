# Generated by Django 5.0.1 on 2025-07-17 08:58

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('text', models.TextField(help_text='The main content of the comment or review', max_length=2000)),
                ('rating', models.PositiveIntegerField(help_text='Star rating from 1 to 5', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('is_approved', models.BooleanField(default=True, help_text='Whether the comment is approved and visible to the public')),
                ('is_flagged', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Whether the comment has been flagged for review')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, help_text='The parent comment if this is a reply', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='comments.comment')),
                ('product', models.ForeignKey(help_text='The product being commented on', on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='products.product')),
                ('user', models.ForeignKey(help_text='The user who posted the comment', on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Comment',
                'verbose_name_plural': 'Comments',
                'db_table': 'product_comments',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['product', 'is_approved'], name='product_com_product_9a5c0e_idx'), models.Index(fields=['user'], name='product_com_user_id_10245c_idx'), models.Index(fields=['parent'], name='product_com_parent__7e6397_idx')],
            },
        ),
    ]
