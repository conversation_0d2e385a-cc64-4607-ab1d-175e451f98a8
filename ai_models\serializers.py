"""
Serializers for AI model interactions, such as logging user behavior.
"""

from rest_framework import serializers
from .models import UserBehaviorLog


class UserBehaviorLogSerializer(serializers.ModelSerializer):
    """
    Serializer for logging user behavior events.
    
    Validates incoming interaction data before saving it to the log
    for use in AI model training and real-time analytics.
    """
    
    class Meta:
        model = UserBehaviorLog
        fields = [
            'product', 'action_type', 'metadata'
        ]
    
    def validate_action_type(self, value):
        """Ensure the action type is valid."""
        valid_actions = [choice[0] for choice in UserBehaviorLog.ACTION_TYPES]
        if value not in valid_actions:
            raise serializers.ValidationError(f"Invalid action_type. Must be one of {valid_actions}")
        return value
