"""
Models for product comments, ratings, and replies.

This module defines the data structure for user-submitted reviews,
ratings, and threaded replies, forming the basis for community
feedback and sentiment analysis.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

User = get_user_model()


class Comment(models.Model):
    """
    Represents a single user comment or review on a product.
    
    Each comment is linked to a user and a product, and includes a rating
    and text content. It supports threading for replies and includes
    fields for moderation and AI analysis.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.CASCADE,
        related_name='comments',
        help_text="The product being commented on"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='comments',
        help_text="The user who posted the comment"
    )
    
    # Threading for replies
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        help_text="The parent comment if this is a reply"
    )
    
    # Comment content
    text = models.TextField(
        max_length=2000,
        help_text="The main content of the comment or review"
    )
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Star rating from 1 to 5"
    )
    
    # Moderation and status
    is_approved = models.BooleanField(
        default=True,
        help_text="Whether the comment is approved and visible to the public"
    )
    is_flagged = models.BooleanField(
        default=False,
        help_text="Whether the comment has been flagged for review"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'product_comments'
        verbose_name = 'Comment'
        verbose_name_plural = 'Comments'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['product', 'is_approved']),
            models.Index(fields=['user']),
            models.Index(fields=['parent']),
        ]
    
    def __str__(self):
        return f"Comment by {self.user.username} on {self.product.name}"
    
    @property
    def is_review(self):
        """A comment is a review if it's a top-level comment."""
        return self.parent is None
    
    @property
    def is_reply(self):
        """A comment is a reply if it has a parent."""
        return self.parent is not None
