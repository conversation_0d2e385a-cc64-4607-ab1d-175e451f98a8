"""
Promotion and discount management views.

Provides secure API endpoints for generating user-specific QR codes,
validating them at stores, and managing promotional campaigns.
"""

from rest_framework import generics, status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.db import transaction
import logging

from .models import Promotion, DiscountQR
from .serializers import (
    PromotionSerializer,
    GenerateQRSerializer,
    DiscountQRSerializer,
    ValidateQRSerializer,
    UserDiscountQRDetailSerializer
)
from products.models import Store
from auth_app.permissions import IsAdminUser, IsStoreOwner

logger = logging.getLogger(__name__)


class UserActivePromotionsView(generics.ListAPIView):
    """
    Lists all active promotions available to the authenticated user.
    
    Filters promotions based on current date, status, and user-specific
    usage limits, providing a personalized list of available offers.
    """
    
    serializer_class = PromotionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return promotions that are active and usable by the current user."""
        now = timezone.now()
        user = self.request.user
        
        # Find promotions the user has already used up to their limit
        used_promotions_ids = DiscountQR.objects.filter(
            generated_by_user=user,
            is_used=True
        ).values_list('promotion_id', flat=True)
        
        # Filter for active promotions excluding those the user has maxed out
        return Promotion.objects.filter(
            status='active',
            start_date__lte=now,
            end_date__gte=now
        ).exclude(id__in=used_promotions_ids)


class GenerateUserQRView(generics.CreateAPIView):
    """
    Generates a master Discount QR code for a user's cart.
    
    Accepts cart data and a promotion ID, validates eligibility,
    and creates a secure, time-sensitive QR code with a digital receipt.
    """
    
    serializer_class = GenerateQRSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def create(self, request, *args, **kwargs):
        """Handle QR code generation with comprehensive validation."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            with transaction.atomic():
                qr_code = serializer.save()
                response_serializer = DiscountQRSerializer(qr_code)
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"QR generation failed for user {request.user.username}: {e}")
            return Response(
                {"error": "Failed to generate QR code. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ValidateStoreQRView(APIView):
    """
    Validates a master QR code for a specific store.
    
    Allows an authenticated store owner to scan/validate a QR code,
    apply the discount for their portion of the sale, and retrieve
    the relevant cart details for their store.
    """
    
    permission_classes = [permissions.IsAuthenticated, IsStoreOwner]
    
    def post(self, request, *args, **kwargs):
        """Handle QR code validation and discount application."""
        serializer = ValidateQRSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        
        qr_code = self.context['qr_code']
        store = self.context['store']
        
        try:
            with transaction.atomic():
                # Calculate discount for this specific store
                store_data = qr_code.digital_receipt_data['store_breakdown'][str(store.id)]
                store_subtotal = float(store_data['subtotal'])
                
                promotion = qr_code.promotion
                discount_applied = 0
                
                if promotion.discount_type == 'percentage':
                    discount_applied = store_subtotal * (float(promotion.discount_value) / 100)
                elif promotion.discount_type == 'fixed_amount':
                    # Distribute fixed amount discount proportionally
                    total_cart_amount = float(qr_code.digital_receipt_data['total_amount'])
                    store_proportion = store_subtotal / total_cart_amount
                    discount_applied = float(promotion.discount_value) * store_proportion
                
                discount_applied = round(discount_applied, 2)
                
                # Mark the store's portion as used
                qr_code.mark_store_as_used(store, discount_applied=discount_applied)
                
                logger.info(f"QR {qr_code.uuid} validated by store {store.name} for user {qr_code.generated_by_user.username}")
                
                return Response({
                    "message": "QR code validated successfully for your store.",
                    "qr_uuid": qr_code.uuid,
                    "store_name": store.name,
                    "discount_to_apply": str(discount_applied),
                    "store_cart_items": store_data['items'],
                    "store_subtotal": str(store_subtotal),
                    "is_fully_used": qr_code.is_used
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            logger.error(f"QR validation failed for store {store.name}: {e}")
            return Response(
                {"error": "Failed to validate QR code. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserQRHistoryView(generics.ListAPIView):
    """
    Retrieves the history of QR codes generated by the authenticated user.
    """
    serializer_class = UserDiscountQRDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return all QR codes generated by the current user."""
        return DiscountQR.objects.filter(
            generated_by_user=self.request.user
        ).select_related('promotion').prefetch_related('store_usages__store').order_by('-generated_at')


# --- Admin/Management Views ---

class PromotionAdminListView(generics.ListCreateAPIView):
    """
    Admin view for listing and creating promotions.
    Requires admin privileges.
    """
    queryset = Promotion.objects.all().order_by('-created_at')
    serializer_class = PromotionSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class PromotionAdminDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Admin view for managing a single promotion.
    Requires admin privileges.
    """
    queryset = Promotion.objects.all()
    serializer_class = PromotionSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]
