# Generated by Django 5.0.1 on 2025-07-17 08:41

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_type', models.CharField(choices=[('customer', 'Customer'), ('store_owner', 'Store Owner'), ('admin', 'System Administrator')], default='customer', help_text='User role determining access permissions', max_length=20)),
                ('phone_number', models.CharField(blank=True, help_text='Contact phone number for notifications and support', max_length=15, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'Enter a valid phone number.')])),
                ('date_of_birth', models.DateField(blank=True, help_text='Used for age-based recommendations and compliance', null=True)),
                ('preferred_categories', models.JSONField(blank=True, default=list, help_text="User's preferred product categories for AI recommendations")),
                ('ai_consent', models.BooleanField(default=False, help_text='User consent for AI-powered personalization and data analysis')),
                ('is_verified', models.BooleanField(default=False, help_text='Email verification status')),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last user activity timestamp for session management')),
                ('store_name', models.CharField(blank=True, help_text='Store name for store owners', max_length=200, null=True)),
                ('business_license', models.CharField(blank=True, help_text='Business license number for store owners', max_length=100, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'auth_users',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address_line_1', models.CharField(blank=True, max_length=255)),
                ('address_line_2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, default='USA', max_length=100)),
                ('budget_range_min', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum budget for product recommendations', max_digits=10, null=True)),
                ('budget_range_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum budget for product recommendations', max_digits=10, null=True)),
                ('email_notifications', models.BooleanField(default=True, help_text='Receive email notifications about promotions and updates')),
                ('sms_notifications', models.BooleanField(default=False, help_text='Receive SMS notifications about orders and promotions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
                'db_table': 'user_profiles',
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['user_type'], name='auth_users_user_ty_a6b94c_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['is_verified'], name='auth_users_is_veri_cd09c1_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['last_activity'], name='auth_users_last_ac_cf9ba0_idx'),
        ),
    ]
