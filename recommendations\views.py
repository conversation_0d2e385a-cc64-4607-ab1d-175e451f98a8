"""
Dedicated API endpoints for AI-powered recommendations.

These views provide clean, organized access to the various recommendation
types generated by the AIRecommendationService.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
import logging

from ai_models.services import AIRecommendationService
from products.models import Product
from products.serializers import ProductListSerializer

logger = logging.getLogger(__name__)


class PersonalizedRecommendationsView(generics.GenericAPIView):
    """
    Returns personalized product recommendations for the authenticated user.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ProductListSerializer
    
    def get(self, request, *args, **kwargs):
        try:
            service = AIRecommendationService()
            recommendations = service.get_personalized_recommendations(
                request.user,
                limit=int(request.query_params.get('limit', 10))
            )
            return Response(recommendations)
        except Exception as e:
            logger.error(f"Error generating personalized recommendations for {request.user.username}: {e}")
            return Response({"error": "Could not generate recommendations."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GeneralRecommendationsView(generics.GenericAPIView):
    """
    Returns general, non-personalized product recommendations (e.g., best-sellers).
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = ProductListSerializer
    
    def get(self, request, *args, **kwargs):
        try:
            service = AIRecommendationService()
            recommendations = service.get_general_recommendations(
                limit=int(request.query_params.get('limit', 10))
            )
            return Response(recommendations)
        except Exception as e:
            logger.error(f"Error generating general recommendations: {e}")
            return Response({"error": "Could not generate recommendations."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SimilarProductsView(generics.GenericAPIView):
    """
    Returns products that are similar to a given product.
    """
    permission_classes = [permissions.AllowAny]
    serializer_class = ProductListSerializer
    
    def get(self, request, product_id, *args, **kwargs):
        try:
            product = get_object_or_404(Product, id=product_id)
            service = AIRecommendationService()
            recommendations = service.get_similar_products(
                product,
                limit=int(request.query_params.get('limit', 5))
            )
            return Response(recommendations)
        except Exception as e:
            logger.error(f"Error generating similar products for {product_id}: {e}")
            return Response({"error": "Could not generate recommendations."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
