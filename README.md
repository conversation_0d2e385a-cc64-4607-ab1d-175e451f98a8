# Best  IN Click - E-commerce Backend

This repository contains the comprehensive backend for the "Best IN Click" e-commerce application. It is built with Django and Django REST Framework, featuring a modular architecture, robust security, and powerful AI-driven functionalities.

## Key Features

- **Modular Design**: The project is organized into logical Django apps (`auth_app`, `products`, `promotions`, `ai_models`, etc.) for maintainability and scalability.
- **Secure Authentication**: Uses JSON Web Tokens (JWT) for secure, stateless authentication for customers, store owners, and admins.
- **Role-Based Access Control (RBAC)**: Strict permissions ensure users can only access data and perform actions appropriate for their role.
- **Advanced Product Management**: Supports complex product catalogs with categories, brands, stores, and custom attributes.
- **Multi-Store Promotions**: A unique system for generating master QR codes for cart-wide discounts that can be validated across multiple stores.
- **AI-Powered Functionality**:
    - Smart Search with query enhancement.
    - Personalized and general product recommendations.
    - AI-driven product and store comparisons.
    - Automated sentiment analysis for product reviews.
- **Asynchronous Task Processing**: Utilizes Celery and Redis for background tasks like report generation and AI model training, ensuring the API remains responsive.
- **Comprehensive Reporting**: Asynchronous generation of detailed reports with AI-generated narrative summaries.

## Tech Stack

- **Backend**: Python 3.10+, Django 5+, Django REST Framework
- **Database**: PostgreSQL (production), SQLite (development)
- **Authentication**: `djangorestframework-simplejwt`
- **Async Tasks**: Celery, Redis
- **AI/ML Libraries (Conceptual)**: `scikit-learn`, `pandas`, `nltk`, `spacy`
- **API Documentation**: `drf-spectacular` for OpenAPI 3 schema generation.

---

## Project Setup

### Prerequisites

- Python 3.10 or higher
- Pip (Python package installer)
- PostgreSQL server (or Docker)
- Redis server (or Docker)
- A virtual environment tool (`venv`, `virtualenv`)

### 1. Clone the Repository

\`\`\`bash
git clone <your-repository-url>
cd best-on-click-backend
\`\`\`

### 2. Set Up a Virtual Environment

It is highly recommended to use a virtual environment to manage project dependencies.

\`\`\`bash
# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
\`\`\`

### 3. Install Dependencies

Install all required Python packages from the `requirements.txt` file.

\`\`\`bash
pip install -r requirements.txt
\`\`\`

### 4. Configure Environment Variables

Create a `.env` file in the root directory of the project. This file will store your secret keys and configuration settings. Copy the contents of `.env.example` (if provided) or create it from scratch.

\`\`\`env
# .env file

# SECURITY WARNING: Change this in production!
SECRET_KEY='your-super-secret-django-key'

# Set to False in production
DEBUG=True

# Your allowed hosts
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (PostgreSQL)
DB_NAME=best_on_click_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Set to True to use SQLite for quick local development
# USE_SQLITE=True

# Redis URL for Celery
REDIS_URL=redis://localhost:6379/0

# Frontend URL for CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
\`\`\`

### 5. Set Up the Database

Run the Django migrations to create the database schema.

\`\`\`bash
python manage.py migrate
\`\`\`

### 6. Create a Superuser

Create an admin account to access the Django admin interface.

\`\`\`bash
python manage.py createsuperuser
\`\`\`

Follow the prompts to set up your username, email, and password.

---

## Running the Application

### 1. Start the Development Server

To run the Django application, use the `runserver` command.

\`\`\`bash
python manage.py runserver
\`\`\`

The backend API will be available at `http://127.0.0.1:8000/`.

### 2. Run the Celery Worker

For asynchronous tasks to be processed, you need to run a Celery worker in a separate terminal. Make sure your Redis server is running first.

\`\`\`bash
# Ensure your virtual environment is activated
celery -A best_on_click worker -l info
\`\`\`

### 3. Accessing the API

- **API Root**: `http://127.0.0.1:8000/api/v1/`
- **API Docs (Swagger UI)**: `http://127.0.0.1:8000/api/docs/`
- **Django Admin**: `http://127.0.0.1:8000/admin/`

---

## API Structure Overview

The API is versioned under `/api/v1/`.

- `/auth/`: User registration, login, profile management.
- `/products/`: Product, category, and brand listings and details.
- `/promotions/`: Endpoints for generating and validating QR codes.
- `/comments/`: Creating and viewing product reviews.
- `/cart/`: Shopping cart management.
- `/recommendations/`: AI-powered product recommendations.
- `/comparisons/`: AI-powered product and store comparisons.
- `/dashboard/`: Endpoints for the store owner dashboard.
- `/reports/`: Endpoints for generating and retrieving reports.
