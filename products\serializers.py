"""
Serializers for product management and API responses.

These serializers handle product data serialization with comprehensive
validation, nested relationships, and AI-optimized field handling.
"""

from rest_framework import serializers
from django.db.models import Avg
from .models import Product, Category, Brand, Store, ProductAttribute


class CategorySerializer(serializers.ModelSerializer):
    """
    Category serializer with hierarchical support.
    
    Handles category data with parent-child relationships
    and subcategory information for navigation.
    """
    
    subcategories = serializers.SerializerMethodField()
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Category
        fields = [
            'id', 'name', 'slug', 'description', 'parent',
            'subcategories', 'product_count', 'is_active',
            'sort_order', 'created_at'
        ]
        read_only_fields = ['id', 'slug', 'created_at']
    
    def get_subcategories(self, obj):
        """Get immediate subcategories."""
        subcategories = obj.subcategories.filter(is_active=True)
        return CategorySerializer(subcategories, many=True, context=self.context).data
    
    def get_product_count(self, obj):
        """Get active product count in this category."""
        return obj.products.filter(is_active=True, in_stock=True).count()


class BrandSerializer(serializers.ModelSerializer):
    """
    Brand serializer with reputation metrics.
    
    Includes AI-calculated brand reputation scores
    and product count for brand analytics.
    """
    
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Brand
        fields = [
            'id', 'name', 'slug', 'description', 'logo_url',
            'website', 'brand_reputation_score', 'product_count',
            'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'slug', 'brand_reputation_score', 'created_at']
    
    def get_product_count(self, obj):
        """Get active product count for this brand."""
        return obj.products.filter(is_active=True, in_stock=True).count()


class StoreSerializer(serializers.ModelSerializer):
    """
    Store serializer with comprehensive store information.
    
    Includes AI-powered metrics and analytics for store
    comparison and customer decision-making.
    """
    
    owner_info = serializers.SerializerMethodField()
    product_count = serializers.SerializerMethodField()
    full_address = serializers.ReadOnlyField()
    
    class Meta:
        model = Store
        fields = [
            'id', 'name', 'slug', 'description', 'owner_info',
            'contact_email', 'contact_phone', 'full_address',
            'average_rating', 'total_orders_count', 'customer_service_score',
            'product_count', 'is_active', 'is_verified', 'created_at'
        ]
        read_only_fields = [
            'id', 'slug', 'average_rating', 'total_orders_count',
            'customer_service_score', 'created_at'
        ]
    
    def get_owner_info(self, obj):
        """Get basic store owner information."""
        return {
            'username': obj.owner.username,
            'full_name': obj.owner.get_full_display_name(),
        }
    
    def get_product_count(self, obj):
        """Get active product count for this store."""
        return obj.products.filter(is_active=True, in_stock=True).count()


class ProductAttributeSerializer(serializers.ModelSerializer):
    """
    Product attribute serializer for specifications.
    
    Handles product attributes used in AI comparisons
    and detailed product information display.
    """
    
    class Meta:
        model = ProductAttribute
        fields = ['name', 'value', 'is_comparable']


class ProductListSerializer(serializers.ModelSerializer):
    """
    Optimized product serializer for list views.
    
    Lightweight serializer for product listings with
    essential information and performance optimization.
    """
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)
    store_name = serializers.CharField(source='store.name', read_only=True)
    discounted_price = serializers.ReadOnlyField()
    is_on_sale = serializers.ReadOnlyField()
    primary_image = serializers.ReadOnlyField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'short_description',
            'category_name', 'brand_name', 'store_name',
            'price', 'discounted_price', 'discount_percentage',
            'is_on_sale', 'in_stock', 'primary_image',
            'average_rating', 'total_reviews', 'is_featured'
        ]


class ProductDetailSerializer(serializers.ModelSerializer):
    """
    Comprehensive product serializer for detail views.
    
    Includes all product information, relationships,
    and AI-optimized data for recommendations and comparisons.
    """
    
    category = CategorySerializer(read_only=True)
    brand = BrandSerializer(read_only=True)
    store = StoreSerializer(read_only=True)
    attributes = ProductAttributeSerializer(many=True, read_only=True)
    discounted_price = serializers.ReadOnlyField()
    is_on_sale = serializers.ReadOnlyField()
    primary_image = serializers.ReadOnlyField()
    
    # AI-related fields
    similar_products = serializers.SerializerMethodField()
    recommendation_score = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'description', 'short_description',
            'category', 'brand', 'store', 'attributes',
            'price', 'discounted_price', 'discount_percentage', 'discount_amount',
            'is_on_sale', 'in_stock', 'stock_quantity', 'image_urls',
            'primary_image', 'view_count', 'purchase_count',
            'average_rating', 'total_reviews', 'ai_tags',
            'similar_products', 'recommendation_score',
            'is_active', 'is_featured', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'slug', 'view_count', 'purchase_count',
            'average_rating', 'total_reviews', 'ai_tags',
            'created_at', 'updated_at'
        ]
    
    def get_similar_products(self, obj):
        """Get AI-recommended similar products."""
        # This would integrate with the AI recommendation service
        # For now, return products from same category as placeholder
        similar = Product.objects.filter(
            category=obj.category,
            is_active=True,
            in_stock=True
        ).exclude(id=obj.id)[:4]
        
        return ProductListSerializer(similar, many=True, context=self.context).data
    
    def get_recommendation_score(self, obj):
        """Get AI-calculated recommendation score for current user."""
        # This would integrate with the AI recommendation service
        # Placeholder implementation
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # Calculate based on user preferences, behavior, etc.
            return 0.85  # Placeholder score
        return 0.0


class ProductCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Product serializer for create and update operations.
    
    Handles product creation and updates with comprehensive
    validation and automatic field population.
    """
    
    attributes = ProductAttributeSerializer(many=True, required=False)
    
    class Meta:
        model = Product
        fields = [
            'name', 'description', 'short_description',
            'category', 'brand', 'price', 'discount_percentage',
            'discount_amount', 'in_stock', 'stock_quantity',
            'image_urls', 'attributes', 'is_featured'
        ]
    
    def validate(self, attrs):
        """Validate product data with business rules."""
        # Ensure discount values are reasonable
        if attrs.get('discount_percentage', 0) > 0 and attrs.get('discount_amount', 0) > 0:
            raise serializers.ValidationError(
                "Cannot apply both percentage and fixed amount discounts simultaneously."
            )
        
        # Validate stock quantity for in-stock products
        if attrs.get('in_stock', True) and attrs.get('stock_quantity', 0) <= 0:
            raise serializers.ValidationError(
                "Stock quantity must be greater than 0 for in-stock products."
            )
        
        return attrs
    
    def create(self, validated_data):
        """Create product with attributes."""
        attributes_data = validated_data.pop('attributes', [])
        
        # Set store to current user's store (for store owners)
        request = self.context.get('request')
        if request and request.user.is_store_owner:
            store = Store.objects.filter(owner=request.user, is_active=True).first()
            if store:
                validated_data['store'] = store
            else:
                raise serializers.ValidationError("No active store found for user.")
        
        product = Product.objects.create(**validated_data)
        
        # Create attributes
        for attr_data in attributes_data:
            ProductAttribute.objects.create(product=product, **attr_data)
        
        return product
    
    def update(self, instance, validated_data):
        """Update product with attributes."""
        attributes_data = validated_data.pop('attributes', [])
        
        # Update product fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update attributes
        if attributes_data:
            # Clear existing attributes and create new ones
            instance.attributes.all().delete()
            for attr_data in attributes_data:
                ProductAttribute.objects.create(product=instance, **attr_data)
        
        return instance
