"""
URL routing for product management endpoints.

Defines all product-related API endpoints including
CRUD operations, search, filtering, and AI-powered features.
"""

from django.urls import path
from . import views

app_name = 'products'

urlpatterns = [
    # Product CRUD operations
    path('', views.ProductListView.as_view(), name='product_list'),
    path('create/', views.ProductCreateView.as_view(), name='product_create'),
    path('<slug:slug>/', views.ProductDetailView.as_view(), name='product_detail'),
    path('<slug:slug>/update/', views.ProductUpdateView.as_view(), name='product_update'),
    path('<slug:slug>/delete/', views.ProductDeleteView.as_view(), name='product_delete'),
    
    # Product management
    path('<slug:slug>/toggle-stock/', views.toggle_product_stock, name='toggle_stock'),
    
    # Categories and brands
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('brands/', views.BrandListView.as_view(), name='brand_list'),
    path('stores/', views.StoreListView.as_view(), name='store_list'),
    
    # Search and discovery
    path('search/suggestions/', views.product_search_suggestions, name='search_suggestions'),
    path('featured/', views.featured_products, name='featured_products'),
]
