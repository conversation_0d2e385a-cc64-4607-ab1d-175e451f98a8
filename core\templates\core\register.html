{% extends 'core/base.html' %}
{% block title %}Register | Best in Click{% endblock %}
{% block content %}
<div class="row justify-content-center align-items-center min-vh-100">
  <div class="col-md-7">
    <div class="card shadow-lg border-0">
      <div class="card-body p-5">
        <div class="text-center mb-4">
          <img src="/static/logo.png" alt="Best in Click" style="height: 60px;">
          <h2 class="mt-2 mb-0" style="font-weight: bold; color: #0d6efd;">Create Your Account</h2>
          <p class="text-muted">Choose your role and fill in your details to get started.</p>
        </div>
        {% if form.errors %}
          <div class="alert alert-danger">
            {% for field in form %}
              {% for error in field.errors %}
                <div>{{ error }}</div>
              {% endfor %}
            {% endfor %}
            {% for error in form.non_field_errors %}
              <div>{{ error }}</div>
            {% endfor %}
          </div>
        {% endif %}
        <ul class="nav nav-tabs mb-4" id="registerTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer" type="button" role="tab" aria-controls="customer" aria-selected="true">Customer</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="storeowner-tab" data-bs-toggle="tab" data-bs-target="#storeowner" type="button" role="tab" aria-controls="storeowner" aria-selected="false">Store Owner</button>
          </li>
        </ul>
        <div class="tab-content" id="registerTabsContent">
          <div class="tab-pane fade show active" id="customer" role="tabpanel" aria-labelledby="customer-tab">
            <form method="post" action="/register/">
              {% csrf_token %}
              <input type="hidden" name="role" value="customer">
              <div class="mb-3">
                <label for="customer_username" class="form-label">Username</label>
                <input type="text" class="form-control {% if form.errors.username %}is-invalid{% endif %}" id="customer_username" name="username" value="{{ request.POST.username }}" required>
                {% if form.errors.username %}<div class="invalid-feedback">{{ form.errors.username.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="customer_email" class="form-label">Email</label>
                <input type="email" class="form-control {% if form.errors.email %}is-invalid{% endif %}" id="customer_email" name="email" value="{{ request.POST.email }}" required>
                {% if form.errors.email %}<div class="invalid-feedback">{{ form.errors.email.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="customer_password" class="form-label">Password</label>
                <input type="password" class="form-control {% if form.errors.password %}is-invalid{% endif %}" id="customer_password" name="password" required>
                {% if form.errors.password %}<div class="invalid-feedback">{{ form.errors.password.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="customer_password_confirm" class="form-label">Confirm Password</label>
                <input type="password" class="form-control {% if form.errors.password %}is-invalid{% endif %}" id="customer_password_confirm" name="password_confirm" required>
              </div>
              <button type="submit" class="btn btn-success w-100">Register as Customer</button>
            </form>
          </div>
          <div class="tab-pane fade" id="storeowner" role="tabpanel" aria-labelledby="storeowner-tab">
            <form method="post" action="/register/">
              {% csrf_token %}
              <input type="hidden" name="role" value="store_owner">
              <div class="mb-3">
                <label for="store_username" class="form-label">Username</label>
                <input type="text" class="form-control {% if form.errors.username %}is-invalid{% endif %}" id="store_username" name="username" value="{{ request.POST.username }}" required>
                {% if form.errors.username %}<div class="invalid-feedback">{{ form.errors.username.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="store_email" class="form-label">Email</label>
                <input type="email" class="form-control {% if form.errors.email %}is-invalid{% endif %}" id="store_email" name="email" value="{{ request.POST.email }}" required>
                {% if form.errors.email %}<div class="invalid-feedback">{{ form.errors.email.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="store_password" class="form-label">Password</label>
                <input type="password" class="form-control {% if form.errors.password %}is-invalid{% endif %}" id="store_password" name="password" required>
                {% if form.errors.password %}<div class="invalid-feedback">{{ form.errors.password.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="store_password_confirm" class="form-label">Confirm Password</label>
                <input type="password" class="form-control {% if form.errors.password %}is-invalid{% endif %}" id="store_password_confirm" name="password_confirm" required>
              </div>
              <div class="mb-3">
                <label for="store_name" class="form-label">Store Name</label>
                <input type="text" class="form-control {% if form.errors.store_name %}is-invalid{% endif %}" id="store_name" name="store_name" value="{{ request.POST.store_name }}" required>
                {% if form.errors.store_name %}<div class="invalid-feedback">{{ form.errors.store_name.0 }}</div>{% endif %}
              </div>
              <div class="mb-3">
                <label for="business_license" class="form-label">Business License</label>
                <input type="text" class="form-control {% if form.errors.business_license %}is-invalid{% endif %}" id="business_license" name="business_license" value="{{ request.POST.business_license }}" required>
                {% if form.errors.business_license %}<div class="invalid-feedback">{{ form.errors.business_license.0 }}</div>{% endif %}
              </div>
              <button type="submit" class="btn btn-primary w-100">Register as Store Owner</button>
            </form>
          </div>
        </div>
        {% if success %}
          <div class="alert alert-success mt-4">Registration successful! You can now <a href="/login/">login</a>.</div>
        {% else %}
        <div class="mt-4 text-center">
          <span class="text-muted">Already have an account?</span>
          <a href="/login/" class="ms-1">Login here</a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
