# Generated by Django 5.0.1 on 2025-07-17 08:58

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Promotion campaign name', max_length=200)),
                ('description', models.TextField(help_text='Detailed promotion description')),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage Discount'), ('fixed_amount', 'Fixed Amount Discount'), ('bogo', 'Buy One Get One'), ('free_shipping', 'Free Shipping')], help_text='Type of discount offered', max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='Discount value (percentage or fixed amount)', max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('start_date', models.DateTimeField(help_text='Promotion start date and time')),
                ('end_date', models.DateTimeField(help_text='Promotion end date and time')),
                ('max_uses_total', models.PositiveIntegerField(blank=True, help_text='Maximum total uses across all users (null = unlimited)', null=True)),
                ('max_uses_per_user', models.PositiveIntegerField(default=1, help_text='Maximum uses per user')),
                ('min_purchase_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Minimum purchase amount to qualify', max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('paused', 'Paused'), ('expired', 'Expired')], default='draft', help_text='Current promotion status', max_length=20)),
                ('current_uses', models.PositiveIntegerField(default=0, help_text='Current total usage count')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(help_text='Admin user who created this promotion', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_promotions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Promotion',
                'verbose_name_plural': 'Promotions',
                'db_table': 'promotions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DiscountQR',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique QR code identifier', primary_key=True, serialize=False)),
                ('is_used', models.BooleanField(default=False, help_text='Whether this QR code has been fully used')),
                ('expires_at', models.DateTimeField(help_text='QR code expiration timestamp')),
                ('digital_receipt_data', models.JSONField(help_text='Complete cart information at QR generation time')),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('first_used_at', models.DateTimeField(blank=True, help_text='Timestamp of first store usage', null=True)),
                ('fully_used_at', models.DateTimeField(blank=True, help_text='Timestamp when all stores completed usage', null=True)),
                ('generated_by_user', models.ForeignKey(help_text='User who generated this QR code', on_delete=django.db.models.deletion.CASCADE, related_name='generated_qr_codes', to=settings.AUTH_USER_MODEL)),
                ('promotion', models.ForeignKey(help_text='Associated promotion', on_delete=django.db.models.deletion.CASCADE, related_name='qr_codes', to='promotions.promotion')),
            ],
            options={
                'verbose_name': 'Discount QR Code',
                'verbose_name_plural': 'Discount QR Codes',
                'db_table': 'discount_qr_codes',
                'ordering': ['-generated_at'],
            },
        ),
        migrations.CreateModel(
            name='PromotionUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('generated', 'QR Code Generated'), ('validated', 'QR Code Validated'), ('used', 'Discount Applied'), ('expired', 'QR Code Expired'), ('fraud_detected', 'Fraud Detected')], help_text='Action performed', max_length=20)),
                ('details', models.JSONField(default=dict, help_text='Additional action details')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Discount amount involved in this action', max_digits=10)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='User IP address for fraud detection', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='User agent string for fraud detection')),
                ('discount_qr', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='usage_logs', to='promotions.discountqr')),
                ('promotion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_logs', to='promotions.promotion')),
                ('store', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='promotion_logs', to='products.store')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='promotion_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Promotion Usage Log',
                'verbose_name_plural': 'Promotion Usage Logs',
                'db_table': 'promotion_usage_logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='StoreDiscountUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_used_by_store', models.BooleanField(default=False, help_text='Whether this store has used the QR code')),
                ('used_at', models.DateTimeField(blank=True, help_text='Timestamp when store used the QR code', null=True)),
                ('discount_applied', models.DecimalField(decimal_places=2, default=0.0, help_text='Actual discount amount applied by this store', max_digits=10)),
                ('store_cart_items', models.JSONField(default=list, help_text='Cart items specific to this store')),
                ('store_subtotal', models.DecimalField(decimal_places=2, default=0.0, help_text='Subtotal for items from this store', max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('discount_qr', models.ForeignKey(help_text='Master QR code being used', on_delete=django.db.models.deletion.CASCADE, related_name='store_usages', to='promotions.discountqr')),
                ('store', models.ForeignKey(help_text='Store using the QR code', on_delete=django.db.models.deletion.CASCADE, related_name='qr_usages', to='products.store')),
            ],
            options={
                'verbose_name': 'Store Discount Usage',
                'verbose_name_plural': 'Store Discount Usages',
                'db_table': 'store_discount_usages',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['status'], name='promotions_status_239aab_idx'),
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['start_date', 'end_date'], name='promotions_start_d_e0aba6_idx'),
        ),
        migrations.AddIndex(
            model_name='promotion',
            index=models.Index(fields=['discount_type'], name='promotions_discoun_803d21_idx'),
        ),
        migrations.AddIndex(
            model_name='discountqr',
            index=models.Index(fields=['is_used'], name='discount_qr_is_used_3cc414_idx'),
        ),
        migrations.AddIndex(
            model_name='discountqr',
            index=models.Index(fields=['expires_at'], name='discount_qr_expires_d2fd7b_idx'),
        ),
        migrations.AddIndex(
            model_name='discountqr',
            index=models.Index(fields=['generated_by_user'], name='discount_qr_generat_1737bf_idx'),
        ),
        migrations.AddIndex(
            model_name='discountqr',
            index=models.Index(fields=['promotion'], name='discount_qr_promoti_11cb65_idx'),
        ),
        migrations.AddIndex(
            model_name='promotionusagelog',
            index=models.Index(fields=['action'], name='promotion_u_action_6f1e99_idx'),
        ),
        migrations.AddIndex(
            model_name='promotionusagelog',
            index=models.Index(fields=['timestamp'], name='promotion_u_timesta_c697d3_idx'),
        ),
        migrations.AddIndex(
            model_name='promotionusagelog',
            index=models.Index(fields=['promotion'], name='promotion_u_promoti_8b45a0_idx'),
        ),
        migrations.AddIndex(
            model_name='promotionusagelog',
            index=models.Index(fields=['user'], name='promotion_u_user_id_8bfde0_idx'),
        ),
        migrations.AddIndex(
            model_name='storediscountusage',
            index=models.Index(fields=['is_used_by_store'], name='store_disco_is_used_01153c_idx'),
        ),
        migrations.AddIndex(
            model_name='storediscountusage',
            index=models.Index(fields=['used_at'], name='store_disco_used_at_d5b6bf_idx'),
        ),
        migrations.AddIndex(
            model_name='storediscountusage',
            index=models.Index(fields=['store'], name='store_disco_store_i_db8862_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='storediscountusage',
            unique_together={('discount_qr', 'store')},
        ),
    ]
