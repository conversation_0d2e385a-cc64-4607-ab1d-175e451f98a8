{% extends 'core/base.html' %}

{% block title %}System Reports{% endblock %}

{% block content %}
<div class="container mt-5">
  <h1 class="mb-4">System Reports</h1>

  <p>Overview of key platform metrics and performance indicators.</p>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-md-3 col-6 mb-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5>Total Users</h5>
          <h3>{{ total_users }}</h3>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-6 mb-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5>Total Stores</h5>
          <h3>{{ total_stores }}</h3>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-6 mb-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5>Total Products</h5>
          <h3>{{ total_products }}</h3>
        </div>
      </div>
    </div>
    <div class="col-md-3 col-6 mb-3">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <h5>Comments</h5>
          <h3>{{ total_comments }}</h3>
        </div>
      </div>
    </div>
  </div>

  <!-- Activity Logs Table (optional) -->
  <h5 class="mt-4">Recent Activity Logs</h5>
  <div class="table-responsive">
    <table class="table table-sm table-bordered">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>User</th>
          <th>Action</th>
          <th>Time</th>
        </tr>
      </thead>
      <tbody>
        {% for log in recent_logs %}
        <tr>
          <td>{{ forloop.counter }}</td>
          <td>{{ log.user.username }}</td>
          <td>{{ log.action }}</td>
          <td>{{ log.timestamp|date:"Y-m-d H:i" }}</td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="4" class="text-center">No logs found.</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>
{% endblock %}
