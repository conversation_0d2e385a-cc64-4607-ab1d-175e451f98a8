"""
Authentication views for Best on Click e-commerce platform.

This module provides secure user authentication, registration,
profile management, and JWT token handling with comprehensive
error handling and security measures.
"""

from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
import logging

from .models import User, UserProfile
from .serializers import (
    UserRegistrationSerializer,
    CustomTokenObtainPairSerializer,
    UserProfileSerializer,
    UserUpdateSerializer,
    PasswordChangeSerializer
)

logger = logging.getLogger(__name__)
User = get_user_model()


class UserRegistrationView(generics.CreateAPIView):
    """
    User registration endpoint with automatic profile creation.
    
    Creates new user accounts with role-based access control
    and sends verification email for enhanced security.
    """
    
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    def create(self, request, *args, **kwargs):
        """Handle user registration with comprehensive validation."""
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            try:
                user = serializer.save()
                
                # Log successful registration
                logger.info(f"New user registered: {user.username} ({user.user_type})")
                
                # Send verification email (conceptual - implement with your email service)
                self._send_verification_email(user)
                
                # Generate tokens for immediate login
                refresh = RefreshToken.for_user(user)
                
                return Response({
                    'message': 'Registration successful. Please check your email for verification.',
                    'user': {
                        'id': str(user.id),
                        'username': user.username,
                        'email': user.email,
                        'user_type': user.user_type,
                        'is_verified': user.is_verified,
                    },
                    'tokens': {
                        'refresh': str(refresh),
                        'access': str(refresh.access_token),
                    }
                }, status=status.HTTP_201_CREATED)
                
            except Exception as e:
                logger.error(f"Registration error: {str(e)}")
                return Response({
                    'error': 'Registration failed. Please try again.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _send_verification_email(self, user):
        """Send verification email to new user."""
        try:
            # Implement email verification logic here
            # This is a placeholder for actual email service integration
            subject = 'Welcome to Best on Click - Verify Your Email'
            message = f'Hello {user.get_full_display_name()}, please verify your email...'
            
            # Uncomment when email service is configured
            # send_mail(subject, message, settings.DEFAULT_FROM_EMAIL, [user.email])
            
            logger.info(f"Verification email sent to {user.email}")
        except Exception as e:
            logger.error(f"Failed to send verification email: {str(e)}")


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom login view with enhanced user information.
    
    Provides JWT tokens along with user profile data
    for frontend state management and personalization.
    """
    
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        """Handle user login with activity tracking."""
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            # Update last activity timestamp
            try:
                username = request.data.get('username')
                user = User.objects.get(username=username)
                user.save(update_fields=['last_activity'])
                
                logger.info(f"User logged in: {username}")
            except User.DoesNotExist:
                pass
        
        return response


class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    User profile management endpoint.
    
    Allows authenticated users to view and update their profile
    information including preferences and address details.
    """
    
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        """Get or create user profile."""
        profile, created = UserProfile.objects.get_or_create(
            user=self.request.user
        )
        return profile
    
    def update(self, request, *args, **kwargs):
        """Handle profile updates with validation."""
        try:
            response = super().update(request, *args, **kwargs)
            logger.info(f"Profile updated for user: {request.user.username}")
            return response
        except Exception as e:
            logger.error(f"Profile update error: {str(e)}")
            return Response({
                'error': 'Profile update failed. Please try again.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserAccountView(generics.RetrieveUpdateAPIView):
    """
    User account information management.
    
    Allows users to update their basic account information
    while maintaining security constraints.
    """
    
    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        """Return current authenticated user."""
        return self.request.user


class PasswordChangeView(APIView):
    """
    Password change endpoint with security validation.
    
    Requires current password verification and enforces
    strong password policies for enhanced security.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Handle password change request."""
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                serializer.save()
                logger.info(f"Password changed for user: {request.user.username}")
                
                return Response({
                    'message': 'Password changed successfully.'
                }, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"Password change error: {str(e)}")
                return Response({
                    'error': 'Password change failed. Please try again.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    """
    User logout endpoint with token blacklisting.
    
    Properly handles JWT token invalidation for secure logout
    and maintains audit trail of user sessions.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Handle user logout with token blacklisting."""
        try:
            refresh_token = request.data.get('refresh_token')
            
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            logger.info(f"User logged out: {request.user.username}")
            
            return Response({
                'message': 'Logout successful.'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response({
                'error': 'Logout failed. Please try again.'
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_dashboard_stats(request):
    """
    Get basic user dashboard statistics.
    
    Provides overview information for user dashboard
    including account status and activity summary.
    """
    try:
        user = request.user
        
        # Get user statistics (expand based on requirements)
        stats = {
            'account_status': {
                'is_verified': user.is_verified,
                'user_type': user.user_type,
                'member_since': user.date_joined.strftime('%Y-%m-%d'),
                'last_activity': user.last_activity.strftime('%Y-%m-%d %H:%M:%S'),
            },
            'preferences': {
                'ai_consent': user.ai_consent,
                'preferred_categories': user.preferred_categories,
            }
        }
        
        return Response(stats, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Dashboard stats error: {str(e)}")
        return Response({
            'error': 'Failed to load dashboard statistics.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
