Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3742
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3793
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\settings.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
D:\best-on-click-backend\best_on_click\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 14
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "products" does not exist
LINE 1: ...NT(*) FROM (SELECT "products"."id" AS "col1" FROM "products"...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\best-on-click-backend\core\views.py", line 17, in home
    if ai_recommendations.count() < 8:
       ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\query.py", line 618, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\sql\query.py", line 618, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\sql\query.py", line 604, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "products" does not exist
LINE 1: ...NT(*) FROM (SELECT "products"."id" AS "col1" FROM "products"...
                                                             ^

"GET / HTTP/1.1" 500 133973
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 3706
"GET /recommendations/ HTTP/1.1" 200 25
"GET /login/ HTTP/1.1" 200 15
"GET /login/ HTTP/1.1" 200 15
"GET /login/ HTTP/1.1" 200 3023
"GET /login/ HTTP/1.1" 200 3400
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /register/ HTTP/1.1" 200 6060
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /register/ HTTP/1.1" 200 6060
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /login/ HTTP/1.1" 200 3400
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /login/ HTTP/1.1" 200 3400
"GET /static/logo.png HTTP/1.1" 404 1884
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /login/ HTTP/1.1" 200 3400
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /register/ HTTP/1.1" 200 7084
"GET /static/logo.png HTTP/1.1" 404 1884
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /register/ HTTP/1.1" 200 7494
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /register/ HTTP/1.1" 200 7418
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /register/ HTTP/1.1" 200 7430
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /register/ HTTP/1.1" 200 7070
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /login/ HTTP/1.1" 200 3654
"GET /static/logo.png HTTP/1.1" 404 1884
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /login/ HTTP/1.1" 200 3652
"GET /static/logo.png HTTP/1.1" 404 1884
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
D:\best-on-click-backend\core\views.py changed, reloading.
D:\best-on-click-backend\core\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /register/ HTTP/1.1" 200 7084
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /register/ HTTP/1.1" 200 7396
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /register/ HTTP/1.1" 200 7076
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /login/ HTTP/1.1" 200 3654
"GET /static/logo.png HTTP/1.1" 404 1884
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6372
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /register/ HTTP/1.1" 200 7084
"GET /static/logo.png HTTP/1.1" 404 1884
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 404 1884
"POST /login/ HTTP/1.1" 200 3654
"GET /static/logo.png HTTP/1.1" 404 1884
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 200 1059033
"GET /login/ HTTP/1.1" 200 3441
"GET /login/ HTTP/1.1" 200 3441
"GET /categories/ HTTP/1.1" 200 20
"POST /login/ HTTP/1.1" 200 3654
"POST /login/ HTTP/1.1" 200 3654
"GET /login/ HTTP/1.1" 200 3441
"GET /register/ HTTP/1.1" 200 7084
"POST /register/ HTTP/1.1" 200 7067
"GET /login/ HTTP/1.1" 200 3441
"POST /login/ HTTP/1.1" 200 3653
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /login/ HTTP/1.1" 200 3441
"GET /admin HTTP/1.1" 301 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4158
"GET /static/admin/css/base.css HTTP/1.1" 200 21544
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2682
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/css/responsive.css HTTP/1.1" 200 17905
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4329
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4329
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4329
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4329
"POST /login/ HTTP/1.1" 200 3654
- Broken pipe from ('127.0.0.1', 29652)
"POST /login/ HTTP/1.1" 200 3653
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4158
- Broken pipe from ('127.0.0.1', 29654)
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4331
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6261
"GET /login/ HTTP/1.1" 200 3441
"POST /login/ HTTP/1.1" 200 3654
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4158
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /static/admin/css/login.css HTTP/1.1" 304 0
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4329
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4319
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 5129
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /admin/ HTTP/1.1" 200 5129
"GET /admin/ HTTP/1.1" 200 5129
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /admin/ HTTP/1.1" 200 5129
"GET /admin/ HTTP/1.1" 200 5129
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /admin/ HTTP/1.1" 200 5129
"GET /admin/ HTTP/1.1" 200 5129
"GET /admin/ HTTP/1.1" 200 5129
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
"GET /register/ HTTP/1.1" 200 7084
"GET /login/ HTTP/1.1" 200 3441
"POST /login/ HTTP/1.1" 200 3654
"POST /login/ HTTP/1.1" 200 3654
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6372
"POST /login/ HTTP/1.1" 200 3654
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6372
"GET /login/ HTTP/1.1" 200 3441
"GET /static/logo.png HTTP/1.1" 200 1059033
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6261
- Broken pipe from ('127.0.0.1', 30838)
"POST /login/ HTTP/1.1" 200 2796
Forbidden (CSRF token from POST incorrect.): /login/
"POST /login/ HTTP/1.1" 403 2517
Forbidden (CSRF token from POST incorrect.): /login/
"POST /login/ HTTP/1.1" 403 2517
Watching for file changes with StatReloader
Forbidden (CSRF token from POST incorrect.): /login/
"POST /login/ HTTP/1.1" 403 2517
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6261
"GET / HTTP/1.1" 200 5607
"GET /static/hero-shopping.svg HTTP/1.1" 404 1911
Not Found: /admin/users
"GET /admin/users HTTP/1.1" 404 4154
D:\best-in-click\admin\views.py changed, reloading.
Watching for file changes with StatReloader
D:\best-in-click\admin\views.py changed, reloading.
Watching for file changes with StatReloader
