"""
Promotion and discount management models for Best on Click.

This module handles promotional campaigns, QR code generation,
and multi-store discount tracking with comprehensive security measures.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid
from datetime import timedelta

User = get_user_model()


class Promotion(models.Model):
    """
    Promotional campaign model with flexible discount structures.
    
    Supports various discount types including percentage, fixed amount,
    and buy-one-get-one offers with comprehensive validation.
    """
    
    DISCOUNT_TYPE_CHOICES = [
        ('percentage', 'Percentage Discount'),
        ('fixed_amount', 'Fixed Amount Discount'),
        ('bogo', 'Buy One Get One'),
        ('free_shipping', 'Free Shipping'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('expired', 'Expired'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic promotion information
    name = models.CharField(
        max_length=200,
        help_text="Promotion campaign name"
    )
    description = models.TextField(
        help_text="Detailed promotion description"
    )
    
    # Discount configuration
    discount_type = models.CharField(
        max_length=20,
        choices=DISCOUNT_TYPE_CHOICES,
        help_text="Type of discount offered"
    )
    discount_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0.01)],
        help_text="Discount value (percentage or fixed amount)"
    )
    
    # Promotion validity
    start_date = models.DateTimeField(
        help_text="Promotion start date and time"
    )
    end_date = models.DateTimeField(
        help_text="Promotion end date and time"
    )
    
    # Usage limitations
    max_uses_total = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum total uses across all users (null = unlimited)"
    )
    max_uses_per_user = models.PositiveIntegerField(
        default=1,
        help_text="Maximum uses per user"
    )
    min_purchase_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0.00)],
        help_text="Minimum purchase amount to qualify"
    )
    
    # Promotion status and metadata
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Current promotion status"
    )
    current_uses = models.PositiveIntegerField(
        default=0,
        help_text="Current total usage count"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_promotions',
        help_text="Admin user who created this promotion"
    )
    
    class Meta:
        db_table = 'promotions'
        verbose_name = 'Promotion'
        verbose_name_plural = 'Promotions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['discount_type']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_discount_type_display()})"
    
    @property
    def is_active(self):
        """Check if promotion is currently active."""
        now = timezone.now()
        return (
            self.status == 'active' and
            self.start_date <= now <= self.end_date and
            (self.max_uses_total is None or self.current_uses < self.max_uses_total)
        )
    
    @property
    def is_expired(self):
        """Check if promotion has expired."""
        return timezone.now() > self.end_date
    
    @property
    def days_remaining(self):
        """Get days remaining for active promotion."""
        if self.is_expired:
            return 0
        return (self.end_date - timezone.now()).days
    
    def can_be_used_by_user(self, user):
        """Check if promotion can be used by specific user."""
        if not self.is_active:
            return False
        
        # Check user-specific usage limit
        user_usage = DiscountQR.objects.filter(
            promotion=self,
            generated_by_user=user,
            is_used=True
        ).count()
        
        return user_usage < self.max_uses_per_user
    
    def increment_usage(self):
        """Increment promotion usage count."""
        self.current_uses += 1
        self.save(update_fields=['current_uses'])


class DiscountQR(models.Model):
    """
    Master QR code model for cart-wide discounts.
    
    Represents the main QR code generated for a user's entire cart,
    which can be validated across multiple stores for distributed discounts.
    """
    
    # Use UUID as primary key for security and uniqueness
    uuid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique QR code identifier"
    )
    
    # Promotion relationship
    promotion = models.ForeignKey(
        Promotion,
        on_delete=models.CASCADE,
        related_name='qr_codes',
        help_text="Associated promotion"
    )
    
    # User and usage tracking
    generated_by_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='generated_qr_codes',
        help_text="User who generated this QR code"
    )
    
    # QR code status
    is_used = models.BooleanField(
        default=False,
        help_text="Whether this QR code has been fully used"
    )
    
    # Expiration management
    expires_at = models.DateTimeField(
        help_text="QR code expiration timestamp"
    )
    
    # Cart data at generation time
    digital_receipt_data = models.JSONField(
        help_text="Complete cart information at QR generation time"
    )
    
    # Usage tracking
    generated_at = models.DateTimeField(auto_now_add=True)
    first_used_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp of first store usage"
    )
    fully_used_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when all stores completed usage"
    )
    
    class Meta:
        db_table = 'discount_qr_codes'
        verbose_name = 'Discount QR Code'
        verbose_name_plural = 'Discount QR Codes'
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['is_used']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['generated_by_user']),
            models.Index(fields=['promotion']),
        ]
    
    def save(self, *args, **kwargs):
        """Set expiration time if not provided."""
        if not self.expires_at:
            # QR codes expire after 24 hours by default
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"QR-{str(self.uuid)[:8]} - {self.promotion.name}"
    
    @property
    def is_expired(self):
        """Check if QR code has expired."""
        return timezone.now() > self.expires_at
    
    @property
    def is_valid(self):
        """Check if QR code is valid for use."""
        return not self.is_used and not self.is_expired
    
    @property
    def stores_involved(self):
        """Get list of stores involved in this QR code."""
        return [
            item['store_info']['name'] 
            for item in self.digital_receipt_data.get('cart_items', [])
        ]
    
    @property
    def total_amount(self):
        """Get total cart amount from digital receipt."""
        return self.digital_receipt_data.get('total_amount', 0.0)
    
    def get_store_usage_status(self, store):
        """Get usage status for specific store."""
        try:
            usage = self.store_usages.get(store=store)
            return {
                'is_used': usage.is_used_by_store,
                'used_at': usage.used_at,
                'discount_applied': usage.discount_applied
            }
        except StoreDiscountUsage.DoesNotExist:
            return {
                'is_used': False,
                'used_at': None,
                'discount_applied': 0.0
            }
    
    def mark_store_as_used(self, store, discount_applied=0.0):
        """Mark QR code as used by specific store."""
        usage, created = StoreDiscountUsage.objects.get_or_create(
            discount_qr=self,
            store=store,
            defaults={
                'is_used_by_store': True,
                'used_at': timezone.now(),
                'discount_applied': discount_applied
            }
        )
        
        if not created and not usage.is_used_by_store:
            usage.is_used_by_store = True
            usage.used_at = timezone.now()
            usage.discount_applied = discount_applied
            usage.save()
        
        # Check if all stores have used the QR code
        total_stores = len(set(self.stores_involved))
        used_stores = self.store_usages.filter(is_used_by_store=True).count()
        
        if used_stores >= total_stores:
            self.is_used = True
            self.fully_used_at = timezone.now()
            self.save(update_fields=['is_used', 'fully_used_at'])
            
            # Increment promotion usage
            self.promotion.increment_usage()


class StoreDiscountUsage(models.Model):
    """
    Tracks individual store usage of master QR codes.
    
    Each store involved in a cart can independently validate
    and use their portion of the master QR discount.
    """
    
    # Relationships
    discount_qr = models.ForeignKey(
        DiscountQR,
        on_delete=models.CASCADE,
        related_name='store_usages',
        help_text="Master QR code being used"
    )
    store = models.ForeignKey(
        'products.Store',
        on_delete=models.CASCADE,
        related_name='qr_usages',
        help_text="Store using the QR code"
    )
    
    # Usage status
    is_used_by_store = models.BooleanField(
        default=False,
        help_text="Whether this store has used the QR code"
    )
    used_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when store used the QR code"
    )
    
    # Financial tracking
    discount_applied = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Actual discount amount applied by this store"
    )
    
    # Store-specific cart data
    store_cart_items = models.JSONField(
        default=list,
        help_text="Cart items specific to this store"
    )
    store_subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Subtotal for items from this store"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'store_discount_usages'
        verbose_name = 'Store Discount Usage'
        verbose_name_plural = 'Store Discount Usages'
        unique_together = ['discount_qr', 'store']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_used_by_store']),
            models.Index(fields=['used_at']),
            models.Index(fields=['store']),
        ]
    
    def __str__(self):
        return f"{self.store.name} - QR-{str(self.discount_qr.uuid)[:8]}"
    
    @property
    def discount_percentage_for_store(self):
        """Calculate discount percentage applied by this store."""
        if self.store_subtotal > 0:
            return (self.discount_applied / self.store_subtotal) * 100
        return 0.0


class PromotionUsageLog(models.Model):
    """
    Comprehensive logging of promotion usage for analytics.
    
    Tracks detailed usage patterns for AI-powered promotion
    optimization and fraud detection.
    """
    
    ACTION_CHOICES = [
        ('generated', 'QR Code Generated'),
        ('validated', 'QR Code Validated'),
        ('used', 'Discount Applied'),
        ('expired', 'QR Code Expired'),
        ('fraud_detected', 'Fraud Detected'),
    ]
    
    # Relationships
    promotion = models.ForeignKey(
        Promotion,
        on_delete=models.CASCADE,
        related_name='usage_logs'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='promotion_logs'
    )
    discount_qr = models.ForeignKey(
        DiscountQR,
        on_delete=models.SET_NULL,
        null=True,
        related_name='usage_logs'
    )
    store = models.ForeignKey(
        'products.Store',
        on_delete=models.SET_NULL,
        null=True,
        related_name='promotion_logs'
    )
    
    # Log details
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        help_text="Action performed"
    )
    details = models.JSONField(
        default=dict,
        help_text="Additional action details"
    )
    
    # Financial impact
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Discount amount involved in this action"
    )
    
    # Metadata
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="User IP address for fraud detection"
    )
    user_agent = models.TextField(
        blank=True,
        help_text="User agent string for fraud detection"
    )
    
    class Meta:
        db_table = 'promotion_usage_logs'
        verbose_name = 'Promotion Usage Log'
        verbose_name_plural = 'Promotion Usage Logs'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['action']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['promotion']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return f"{self.get_action_display()} - {self.promotion.name}"
