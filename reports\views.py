"""
API endpoints for generating and retrieving reports.
"""

from rest_framework import generics, status, permissions
from rest_framework.response import Response

from .models import GeneratedReport
from .serializers import GeneratedReportSerializer, GenerateReportSerializer
from .tasks import generate_comprehensive_report_task
from auth_app.permissions import <PERSON><PERSON><PERSON>Own<PERSON>, IsAdminUser
from products.models import Store

class ReportGenerationView(generics.CreateAPIView):
    """
    Triggers the asynchronous generation of a new report.
    """
    serializer_class = GenerateReportSerializer
    permission_classes = [permissions.IsAuthenticated, IsStoreOwner | IsAdminUser]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = request.user
        store = None
        
        if user.is_store_owner:
            store = Store.objects.filter(owner=user, is_active=True).first()
            if not store:
                return Response({"error": "No active store found for this user."}, status=status.HTTP_404_NOT_FOUND)
        
        report = GeneratedReport.objects.create(
            generated_by=user,
            store=store,
            report_type=serializer.validated_data['report_type']
        )
        
        # Launch the Celery task
        generate_comprehensive_report_task.delay(report.id)
        
        response_serializer = GeneratedReportSerializer(report)
        return Response(response_serializer.data, status=status.HTTP_202_ACCEPTED)


class ReportStatusView(generics.RetrieveAPIView):
    """
    Checks the status of a generated report and provides the download URL when complete.
    """
    queryset = GeneratedReport.objects.all()
    serializer_class = GeneratedReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Ensure users can only access their own reports."""
        user = self.request.user
        if user.is_system_admin:
            return GeneratedReport.objects.all()
        if user.is_store_owner:
            return GeneratedReport.objects.filter(store__owner=user)
        return GeneratedReport.objects.filter(generated_by=user)


class ReportHistoryView(generics.ListAPIView):
    """
    Lists all reports generated by or for the user.
    """
    serializer_class = GeneratedReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Ensure users can only access their own reports."""
        user = self.request.user
        if user.is_system_admin:
            return GeneratedReport.objects.all().order_by('-generated_at')
        if user.is_store_owner:
            return GeneratedReport.objects.filter(store__owner=user).order_by('-generated_at')
        return GeneratedReport.objects.filter(generated_by=user).order_by('-generated_at')
