"""
Models for the shopping cart functionality.
"""

from django.db import models
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class Cart(models.Model):
    """
    Represents a user's shopping cart.
    
    Can be associated with an authenticated user or a guest session.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='cart'
    )
    session_key = models.CharField(max_length=40, null=True, blank=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        if self.user:
            return f"Cart for {self.user.username}"
        return f"Guest Cart (Session: {self.session_key})"
    
    @property
    def total_price(self):
        """Calculate the total price of all items in the cart."""
        return sum(item.total_price for item in self.items.all())
    
    @property
    def total_items(self):
        """Calculate the total number of items in the cart."""
        return sum(item.quantity for item in self.items.all())


class CartItem(models.Model):
    """
    Represents a single item within a shopping cart.
    """
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    added_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('cart', 'product')
    
    def __str__(self):
        return f"{self.quantity} x {self.product.name} in cart {self.cart.id}"
    
    @property
    def total_price(self):
        """Calculate the total price for this cart item."""
        return self.product.discounted_price * self.quantity
