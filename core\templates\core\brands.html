{% extends 'core/base.html' %}
{% block title %}Brands - Best in Click{% endblock %}
{% block content %}
<style>
  .brand-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: none;
    border-radius: 1rem;
    overflow: hidden;
  }
  .brand-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }
  .brand-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 50%;
    border: 3px solid #f8f9fa;
  }
  .brand-rating {
    color: #ffc107;
  }
  .alphabet-filter {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 2rem;
  }
  .alphabet-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
    background: white;
    margin: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.2s;
  }
  .alphabet-btn:hover, .alphabet-btn.active {
    background: #0d6efd;
    color: white;
    border-color: #0d6efd;
  }
  .featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
  }
</style>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/">Home</a></li>
    <li class="breadcrumb-item active" aria-current="page">Brands</li>
  </ol>
</nav>

<!-- Page Header -->
<div class="row mb-4">
  <div class="col-md-8">
    <h1 class="display-5 fw-bold text-primary">Explore Brands</h1>
    <p class="lead text-muted">Discover your favorite brands and explore new ones from around the world.</p>
  </div>
  <div class="col-md-4">
    <div class="input-group">
      <input type="text" class="form-control" placeholder="Search brands..." id="brandSearch">
      <button class="btn btn-outline-primary" type="button">
        <i class="fas fa-search"></i>
      </button>
    </div>
  </div>
</div>

<!-- Alphabet Filter -->
<div class="alphabet-filter text-center">
  <div class="mb-2">
    <small class="text-muted fw-bold">BROWSE BY LETTER:</small>
  </div>
  <div>
    <a href="#" class="alphabet-btn active" data-letter="all">All</a>
    {% for letter in "ABCDEFGHIJKLMNOPQRSTUVWXYZ" %}
      <a href="#" class="alphabet-btn" data-letter="{{ letter }}">{{ letter }}</a>
    {% endfor %}
  </div>
</div>

<!-- Filter and Sort Options -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="d-flex gap-2 flex-wrap">
      <button class="btn btn-outline-primary btn-sm active" data-filter="all">All Brands</button>
      <button class="btn btn-outline-primary btn-sm" data-filter="featured">Featured</button>
      <button class="btn btn-outline-primary btn-sm" data-filter="popular">Most Popular</button>
      <button class="btn btn-outline-primary btn-sm" data-filter="new">New Brands</button>
    </div>
  </div>
  <div class="col-md-6 text-end">
    <select class="form-select" id="sortSelect" style="width: auto; display: inline-block;">
      <option value="name">Sort by Name</option>
      <option value="rating">Highest Rated</option>
      <option value="products">Most Products</option>
      <option value="newest">Newest First</option>
    </select>
  </div>
</div>

<!-- Featured Brands Section -->
{% if featured_brands %}
<div class="mb-5">
  <h3 class="mb-4">⭐ Featured Brands</h3>
  <div class="row">
    {% for brand in featured_brands %}
      <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
        <div class="card brand-card h-100 text-center position-relative">
          <div class="featured-badge">
            <i class="fas fa-star"></i> Featured
          </div>
          <div class="card-body">
            {% if brand.logo %}
              <img src="{{ brand.logo }}" alt="{{ brand.name }}" class="brand-logo mb-3">
            {% else %}
              <div class="brand-logo mb-3 d-flex align-items-center justify-content-center bg-light">
                <i class="fas fa-store fa-2x text-muted"></i>
              </div>
            {% endif %}
            <h6 class="card-title">{{ brand.name }}</h6>
            <div class="brand-rating mb-2">
              {% for i in "12345" %}
                {% if forloop.counter <= brand.average_rating %}
                  <i class="fas fa-star"></i>
                {% else %}
                  <i class="far fa-star"></i>
                {% endif %}
              {% endfor %}
              <small class="text-muted">({{ brand.review_count }})</small>
            </div>
            <small class="text-muted">{{ brand.product_count }} products</small>
          </div>
          <div class="card-footer bg-transparent border-0">
            <a href="/brands/{{ brand.slug }}/" class="btn btn-primary btn-sm">View Products</a>
          </div>
        </div>
      </div>
    {% endfor %}
  </div>
</div>
{% endif %}

<!-- All Brands Grid -->
<div class="row" id="brandsGrid">
  {% for brand in brands %}
    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4 brand-item"
         data-name="{{ brand.name|lower }}"
         data-letter="{{ brand.name.0|upper }}"
         data-rating="{{ brand.average_rating|default:0 }}"
         data-products="{{ brand.product_count|default:0 }}">
      <div class="card brand-card h-100 text-center">
        <div class="card-body">
          {% if brand.logo %}
            <img src="{{ brand.logo }}" alt="{{ brand.name }}" class="brand-logo mb-3">
          {% else %}
            <div class="brand-logo mb-3 d-flex align-items-center justify-content-center bg-light mx-auto">
              <i class="fas fa-store fa-2x text-muted"></i>
            </div>
          {% endif %}
          <h6 class="card-title">{{ brand.name }}</h6>
          {% if brand.average_rating %}
            <div class="brand-rating mb-2">
              {% for i in "12345" %}
                {% if forloop.counter <= brand.average_rating %}
                  <i class="fas fa-star"></i>
                {% else %}
                  <i class="far fa-star"></i>
                {% endif %}
              {% endfor %}
              <small class="text-muted">({{ brand.review_count|default:0 }})</small>
            </div>
          {% endif %}
          <small class="text-muted">{{ brand.product_count|default:0 }} products</small>
          {% if brand.country %}
            <br><small class="text-muted"><i class="fas fa-map-marker-alt"></i> {{ brand.country }}</small>
          {% endif %}
        </div>
        <div class="card-footer bg-transparent border-0">
          <a href="/brands/{{ brand.slug }}/" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-eye me-1"></i>View
          </a>
        </div>
      </div>
    </div>
  {% empty %}
    <div class="col-12 text-center py-5">
      <i class="fas fa-store-slash fa-3x text-muted mb-3"></i>
      <h4 class="text-muted">No brands found</h4>
      <p class="text-muted">Brands will appear here once they are added.</p>
    </div>
  {% endfor %}
</div>

<!-- Load More Button -->
{% if has_more_brands %}
<div class="text-center mt-4">
  <button class="btn btn-outline-primary" id="loadMoreBtn">
    <i class="fas fa-plus me-2"></i>Load More Brands
  </button>
</div>
{% endif %}

<script>
// Brand search functionality
document.getElementById('brandSearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const brandItems = document.querySelectorAll('.brand-item');

    brandItems.forEach(item => {
        const brandName = item.getAttribute('data-name');
        if (brandName.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Alphabet filter functionality
document.querySelectorAll('.alphabet-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
        e.preventDefault();

        // Remove active class from all alphabet buttons
        document.querySelectorAll('.alphabet-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        const letter = this.getAttribute('data-letter');
        const brandItems = document.querySelectorAll('.brand-item');

        brandItems.forEach(item => {
            const itemLetter = item.getAttribute('data-letter');
            if (letter === 'all' || itemLetter === letter) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
});

// Sort functionality
document.getElementById('sortSelect').addEventListener('change', function() {
    const sortBy = this.value;
    const grid = document.getElementById('brandsGrid');
    const items = Array.from(grid.querySelectorAll('.brand-item'));

    items.sort((a, b) => {
        if (sortBy === 'name') {
            return a.getAttribute('data-name').localeCompare(b.getAttribute('data-name'));
        } else if (sortBy === 'rating') {
            return parseFloat(b.getAttribute('data-rating')) - parseFloat(a.getAttribute('data-rating'));
        } else if (sortBy === 'products') {
            return parseInt(b.getAttribute('data-products')) - parseInt(a.getAttribute('data-products'));
        }
        return 0;
    });

    items.forEach(item => grid.appendChild(item));
});
</script>
{% endblock %}
