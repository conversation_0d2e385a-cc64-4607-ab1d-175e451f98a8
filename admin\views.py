from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.models import User
from django.db.models import Count

from stores.models import Store
from products.models import Product
from comments.models import Comment
# from core.models import ActivityLog  # إذا كان موجودًا

# Dashboard View
@staff_member_required
def admin_dashboard(request):
    return render(request, 'admin/admin_dashboard.html')

# Manage Users View
@staff_member_required
def manage_users(request):
    users = User.objects.all()
    return render(request, 'admin/manage_users.html', {'users': users})

# Manage Stores View
@staff_member_required
def manage_stores(request):
    stores = Store.objects.all()
    return render(request, 'admin/manage_stores.html', {'stores': stores})

# Manage Products View
@staff_member_required
def manage_products(request):
    products = Product.objects.all()
    return render(request, 'admin/manage_products.html', {'products': products})

# Review Comments View
@staff_member_required
def review_comments(request):
    comments = Comment.objects.select_related('user', 'product').all()
    return render(request, 'admin/review_comments.html', {'comments': comments})

# System Reports View
@staff_member_required
def system_reports(request):
    context = {
        'total_users': User.objects.count(),
        'total_stores': Store.objects.count(),
        'total_products': Product.objects.count(),
        'total_comments': Comment.objects.count(),
        'recent_logs': ActivityLog.objects.select_related('user').order_by('-timestamp')[:10] if ActivityLog.objects.exists() else [],
    }
    return render(request, 'admin/system_reports.html', context)
