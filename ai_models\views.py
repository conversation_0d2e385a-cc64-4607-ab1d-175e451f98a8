"""
Views for handling AI-related data, such as logging user behavior.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
import logging

from .serializers import UserBehaviorLogSerializer

logger = logging.getLogger(__name__)


class LogUserBehaviorView(generics.CreateAPIView):
    """
    Endpoint for logging user behavior from the frontend.
    
    This is a crucial endpoint that allows the client application to
    send interaction data (clicks, views, etc.) to the backend, which
    is then used to power the AI models.
    """
    
    serializer_class = UserBehaviorLogSerializer
    permission_classes = [permissions.AllowAny] # Allow guests to be tracked
    
    def perform_create(self, serializer):
        """
        Save the behavior log, associating it with the user and session.
        """
        user = self.request.user if self.request.user.is_authenticated else None
        
        # A robust session ID should be managed by the frontend and sent in headers/payload
        session_id = self.request.session.session_key
        if not session_id:
            self.request.session.create()
            session_id = self.request.session.session_key
        
        serializer.save(user=user, session_id=session_id)
    
    def create(self, request, *args, **kwargs):
        """
        Handle the creation of a behavior log entry.
        """
        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Error logging user behavior: {e}")
            # Fail silently to not disrupt user experience
            return Response(status=status.HTTP_204_NO_CONTENT)
