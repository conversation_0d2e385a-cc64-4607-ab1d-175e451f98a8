"""
API endpoints for the Store Owner Dashboard.

This module provides store owners with a suite of tools to monitor
their store's performance, manage products, and gain AI-powered
insights into sales trends and customer behavior.
"""

from rest_framework import views, response, status, permissions
from django.db.models import Sum, Count, Avg
from django.utils import timezone
from datetime import timedelta
import logging

from products.models import Product, Store
from promotions.models import StoreDiscountUsage
from comments.models import Comment
from auth_app.permissions import IsStoreOwner
from products.serializers import ProductListSerializer

logger = logging.getLogger(__name__)


class StoreDashboardStatsView(views.APIView):
    """
    Provides comprehensive statistics for a store owner's dashboard.
    
    Aggregates key performance indicators (KPIs) such as total sales,
    order volume, top-selling products, and customer ratings to give
    store owners a real-time overview of their business health.
    """
    permission_classes = [permissions.IsAuthenticated, IsStoreOwner]
    
    def get(self, request, *args, **kwargs):
        try:
            store = self._get_user_store(request.user)
            if not store:
                return response.Response({"error": "No active store found for this user."}, status=status.HTTP_404_NOT_FOUND)
            
            stats = {
                "store_info": self._get_store_info(store),
                "sales_overview": self._get_sales_overview(store),
                "product_overview": self._get_product_overview(store),
                "customer_feedback": self._get_customer_feedback(store),
                "ai_insights": self._get_ai_insights(store),
            }
            
            return response.Response(stats)
            
        except Exception as e:
            logger.error(f"Error generating dashboard stats for user {request.user.username}: {e}")
            return response.Response({"error": "An error occurred while generating dashboard statistics."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _get_user_store(self, user):
        """Safely retrieve the user's active store."""
        return Store.objects.filter(owner=user, is_active=True).first()
    
    def _get_store_info(self, store):
        """Get basic information about the store."""
        return {
            "name": store.name,
            "is_verified": store.is_verified,
            "member_since": store.created_at.strftime("%Y-%m-%d"),
        }
    
    def _get_sales_overview(self, store):
        """
        Conceptual: Calculate sales overview.
        In a real app, this would query an 'Order' model.
        Here, we'll simulate it based on discount usage.
        """
        last_30_days = timezone.now() - timedelta(days=30)
        
        # This is a conceptual placeholder for actual sales data from an Order model
        total_sales = StoreDiscountUsage.objects.filter(
            store=store, used_at__gte=last_30_days
        ).aggregate(total=Sum('store_subtotal'))['total'] or 0
        
        total_orders = StoreDiscountUsage.objects.filter(
            store=store, used_at__gte=last_30_days
        ).count()
        
        return {
            "total_sales_30d": f"${total_sales:.2f}",
            "total_orders_30d": total_orders,
            "average_order_value": f"${(total_sales / total_orders):.2f}" if total_orders > 0 else "$0.00",
        }
    
    def _get_product_overview(self, store):
        """Get statistics about the store's products."""
        products = Product.objects.filter(store=store)
        active_products = products.filter(is_active=True)
        
        top_selling_products = active_products.order_by('-purchase_count')[:5]
        most_viewed_products = active_products.order_by('-view_count')[:5]
        
        return {
            "total_products": products.count(),
            "active_products": active_products.count(),
            "out_of_stock": active_products.filter(in_stock=False).count(),
            "top_selling_products": ProductListSerializer(top_selling_products, many=True).data,
            "most_viewed_products": ProductListSerializer(most_viewed_products, many=True).data,
        }
    
    def _get_customer_feedback(self, store):
        """Get statistics about customer feedback and ratings."""
        comments = Comment.objects.filter(product__store=store, is_approved=True)
        
        return {
            "average_rating": comments.aggregate(avg=Avg('rating'))['avg'] or 0,
            "total_reviews": comments.filter(parent__isnull=True).count(),
            "total_replies": comments.filter(parent__isnull=False).count(),
            "unanswered_comments": comments.filter(replies__isnull=True).count(),
        }
    
    def _get_ai_insights(self, store):
        """
        Conceptual: Provide AI-generated insights for the store owner.
        """
        # This would be generated by a dedicated AI analytics service.
        return {
            "summary": "Your electronics category is performing 25% above average this month.",
            "recommendation": "Consider running a promotion on 'Smart Watches' as they have high view counts but low conversion rates.",
            "positive_sentiment_trend": "Positive sentiment on your 'Wireless Headphones' has increased by 15% in the last week.",
        }
