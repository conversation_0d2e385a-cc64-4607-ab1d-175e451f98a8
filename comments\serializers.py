"""
Serializers for handling product comments and replies.
"""

from rest_framework import serializers
from .models import Comment
from auth_app.serializers import UserUpdateSerializer as BasicUserSerializer
from ai_models.models import AISentimentAnalysisResult


class SentimentResultSerializer(serializers.ModelSerializer):
    """Serializer for displaying sentiment analysis results."""
    class Meta:
        model = AISentimentAnalysisResult
        fields = ['sentiment', 'confidence_score', 'analyzed_at']


class ReplySerializer(serializers.ModelSerializer):
    """Serializer for nested comment replies."""
    user = BasicUserSerializer(read_only=True)
    
    class Meta:
        model = Comment
        fields = ['id', 'user', 'text', 'created_at', 'is_approved']


class CommentSerializer(serializers.ModelSerializer):
    """
    Serializer for product comments, including nested replies.
    """
    user = BasicUserSerializer(read_only=True)
    replies = ReplySerializer(many=True, read_only=True)
    sentiment_result = SentimentResultSerializer(read_only=True)
    
    class Meta:
        model = Comment
        fields = [
            'id', 'user', 'product', 'text', 'rating', 'parent',
            'replies', 'sentiment_result', 'created_at', 'is_approved'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'is_approved', 'sentiment_result']
        extra_kwargs = {
            'product': {'write_only': True},
            'parent': {'write_only': True, 'required': False},
        }
    
    def validate(self, attrs):
        """
        Validate that a user can only post one top-level review per product.
        """
        request = self.context['request']
        user = request.user
        product = attrs.get('product')
        parent = attrs.get('parent')
        
        # This validation only applies to top-level reviews, not replies
        if product and not parent:
            if Comment.objects.filter(product=product, user=user, parent__isnull=True).exists():
                raise serializers.ValidationError("You have already submitted a review for this product.")
        
        return attrs
