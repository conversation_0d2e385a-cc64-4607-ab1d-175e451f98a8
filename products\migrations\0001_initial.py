# Generated by Django 5.0.1 on 2025-07-17 08:58

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Brand name', max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly brand identifier', max_length=120, unique=True)),
                ('description', models.TextField(blank=True, help_text='Brand description and history')),
                ('logo_url', models.URLField(blank=True, help_text='Brand logo image URL')),
                ('website', models.URLField(blank=True, help_text='Official brand website')),
                ('brand_reputation_score', models.FloatField(default=0.0, help_text='AI-calculated brand reputation score (0-10)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(10.0)])),
                ('is_active', models.BooleanField(default=True, help_text='Brand visibility status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Brand',
                'verbose_name_plural': 'Brands',
                'db_table': 'product_brands',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['is_active'], name='product_bra_is_acti_f8f423_idx'), models.Index(fields=['brand_reputation_score'], name='product_bra_brand_r_51b152_idx')],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Category name for product classification', max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly category identifier', max_length=120, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed category description for SEO and user guidance')),
                ('ai_keywords', models.JSONField(blank=True, default=list, help_text='Keywords for AI-powered search and recommendations')),
                ('is_active', models.BooleanField(default=True, help_text='Category visibility status')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Display order for category listing')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, help_text='Parent category for hierarchical organization', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='products.category')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'db_table': 'product_categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Product name', max_length=200)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly product identifier', max_length=220, unique=True)),
                ('description', models.TextField(help_text='Detailed product description')),
                ('short_description', models.CharField(blank=True, help_text='Brief product summary for listings', max_length=500)),
                ('price', models.DecimalField(decimal_places=2, help_text='Product price', max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('discount_percentage', models.FloatField(default=0.0, help_text='Discount percentage (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Fixed discount amount', max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('in_stock', models.BooleanField(default=True, help_text='Product availability status')),
                ('stock_quantity', models.PositiveIntegerField(default=0, help_text='Available stock quantity')),
                ('image_urls', models.JSONField(blank=True, default=list, help_text='List of product image URLs')),
                ('ai_tags', models.JSONField(blank=True, default=list, help_text='AI-generated tags for enhanced search and recommendations')),
                ('similarity_vector', models.JSONField(blank=True, default=list, help_text='AI-generated similarity vector for recommendations')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Total product views')),
                ('purchase_count', models.PositiveIntegerField(default=0, help_text='Total purchases')),
                ('average_rating', models.FloatField(default=0.0, help_text='Average customer rating (0-5 stars)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(5.0)])),
                ('total_reviews', models.PositiveIntegerField(default=0, help_text='Total number of reviews')),
                ('is_active', models.BooleanField(default=True, help_text='Product visibility status')),
                ('is_featured', models.BooleanField(default=False, help_text='Featured product status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand', models.ForeignKey(blank=True, help_text='Product brand', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='products.brand')),
                ('category', models.ForeignKey(help_text='Product category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='products.category')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'db_table': 'products',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Attribute name (e.g., 'Color', 'Size', 'Weight')", max_length=100)),
                ('value', models.CharField(help_text='Attribute value', max_length=200)),
                ('is_comparable', models.BooleanField(default=True, help_text='Whether this attribute should be used in AI comparisons')),
                ('product', models.ForeignKey(help_text='Product this attribute belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='attributes', to='products.product')),
            ],
            options={
                'verbose_name': 'Product Attribute',
                'verbose_name_plural': 'Product Attributes',
                'db_table': 'product_attributes',
            },
        ),
        migrations.CreateModel(
            name='Store',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Store name', max_length=200)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly store identifier', max_length=220, unique=True)),
                ('description', models.TextField(help_text='Store description and policies')),
                ('contact_email', models.EmailField(help_text='Store contact email', max_length=254)),
                ('contact_phone', models.CharField(blank=True, help_text='Store contact phone number', max_length=20)),
                ('address_line_1', models.CharField(max_length=255)),
                ('address_line_2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('postal_code', models.CharField(max_length=20)),
                ('country', models.CharField(default='USA', max_length=100)),
                ('average_rating', models.FloatField(default=0.0, help_text='Average customer rating (0-5 stars)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(5.0)])),
                ('total_orders_count', models.PositiveIntegerField(default=0, help_text='Total number of orders processed')),
                ('customer_service_score', models.FloatField(default=0.0, help_text='AI-calculated customer service quality score (0-10)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(10.0)])),
                ('is_active', models.BooleanField(default=True, help_text='Store operational status')),
                ('is_verified', models.BooleanField(default=False, help_text='Store verification status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(help_text='Store owner/manager', on_delete=django.db.models.deletion.CASCADE, related_name='owned_stores', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Store',
                'verbose_name_plural': 'Stores',
                'db_table': 'stores',
                'ordering': ['-average_rating', 'name'],
            },
        ),
        migrations.AddField(
            model_name='product',
            name='store',
            field=models.ForeignKey(help_text='Store selling this product', on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.store'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['is_active'], name='product_cat_is_acti_f5a665_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['parent'], name='product_cat_parent__fb81c8_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['sort_order'], name='product_cat_sort_or_7126b6_idx'),
        ),
        migrations.AddIndex(
            model_name='productattribute',
            index=models.Index(fields=['name'], name='product_att_name_8a11ad_idx'),
        ),
        migrations.AddIndex(
            model_name='productattribute',
            index=models.Index(fields=['is_comparable'], name='product_att_is_comp_afe45c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='productattribute',
            unique_together={('product', 'name')},
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['is_active'], name='stores_is_acti_89b637_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['is_verified'], name='stores_is_veri_4d15b2_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['average_rating'], name='stores_average_9c1ffb_idx'),
        ),
        migrations.AddIndex(
            model_name='store',
            index=models.Index(fields=['customer_service_score'], name='stores_custome_2dc31e_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_active'], name='products_is_acti_cb485f_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_featured'], name='products_is_feat_19b203_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['in_stock'], name='products_in_stoc_c0670b_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category'], name='products_categor_4083ff_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['brand'], name='products_brand_i_0d1950_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['store'], name='products_store_i_4a84a2_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['price'], name='products_price_fe467e_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['average_rating'], name='products_average_f74e0c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['view_count'], name='products_view_co_bafc9d_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['purchase_count'], name='products_purchas_f0579d_idx'),
        ),
    ]
