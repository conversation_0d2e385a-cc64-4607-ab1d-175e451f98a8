"""
Serializers for promotion and discount management.

These serializers handle data validation and serialization for promotions,
QR code generation, and multi-store discount validation with robust security.
"""

from rest_framework import serializers
from django.utils import timezone
from django.db import transaction
import logging

from .models import Promotion, DiscountQR, StoreDiscountUsage
from products.models import Product, Store
from products.serializers import ProductListSerializer

logger = logging.getLogger(__name__)


class PromotionSerializer(serializers.ModelSerializer):
    """
    Serializer for managing promotional campaigns (Admin/Owner).
    
    Handles creation, update, and retrieval of promotions with
    comprehensive validation of discount rules and validity periods.
    """
    
    is_active = serializers.ReadOnlyField()
    days_remaining = serializers.ReadOnlyField()
    
    class Meta:
        model = Promotion
        fields = [
            'id', 'name', 'description', 'discount_type', 'discount_value',
            'start_date', 'end_date', 'max_uses_total', 'max_uses_per_user',
            'min_purchase_amount', 'status', 'current_uses', 'is_active',
            'days_remaining', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'current_uses', 'created_by', 'created_at', 'updated_at'
        ]
    
    def validate(self, attrs):
        """Validate promotion data with business rules."""
        start_date = attrs.get('start_date', self.instance.start_date if self.instance else None)
        end_date = attrs.get('end_date', self.instance.end_date if self.instance else None)
        
        if start_date and end_date and start_date >= end_date:
            raise serializers.ValidationError("End date must be after start date.")
        
        discount_type = attrs.get('discount_type', self.instance.discount_type if self.instance else None)
        discount_value = attrs.get('discount_value', self.instance.discount_value if self.instance else None)
        
        if discount_type == 'percentage' and not (0 < discount_value <= 100):
            raise serializers.ValidationError("Percentage discount must be between 0 and 100.")
        
        return attrs


class GenerateQRSerializer(serializers.Serializer):
    """
    Serializer for generating a master Discount QR code.
    
    Validates incoming cart data and promotion eligibility before
    creating a secure, time-sensitive QR code for the user.
    """
    
    promotion_id = serializers.UUIDField(required=True)
    cart_items = serializers.ListField(
        child=serializers.DictField(),
        required=True,
        min_length=1
    )
    
    def validate_promotion_id(self, value):
        """Validate that the promotion is active and usable."""
        try:
            promotion = Promotion.objects.get(id=value)
            if not promotion.is_active:
                raise serializers.ValidationError("This promotion is not currently active.")
            
            user = self.context['request'].user
            if not promotion.can_be_used_by_user(user):
                raise serializers.ValidationError("You have reached the usage limit for this promotion.")
            
            return promotion
        except Promotion.DoesNotExist:
            raise serializers.ValidationError("Invalid promotion ID.")
    
    def validate_cart_items(self, value):
        """Validate cart items and calculate total amount."""
        total_amount = 0
        product_ids = [item.get('product_id') for item in value]
        
        # Fetch all products in a single query for efficiency
        products = Product.objects.filter(id__in=product_ids, is_active=True, in_stock=True)
        product_map = {str(p.id): p for p in products}
        
        for item in value:
            product_id = item.get('product_id')
            quantity = item.get('quantity')
            
            if not product_id or not quantity or not isinstance(quantity, int) or quantity <= 0:
                raise serializers.ValidationError("Invalid cart item format.")
            
            product = product_map.get(product_id)
            if not product:
                raise serializers.ValidationError(f"Product with ID {product_id} not found or is unavailable.")
            
            if product.stock_quantity < quantity:
                raise serializers.ValidationError(f"Not enough stock for {product.name}.")
            
            total_amount += product.discounted_price * quantity
        
        self.context['total_amount'] = total_amount
        return value
    
    def validate(self, attrs):
        """Validate total purchase amount against promotion rules."""
        promotion = attrs['promotion_id']
        total_amount = self.context.get('total_amount', 0)
        
        if total_amount < promotion.min_purchase_amount:
            raise serializers.ValidationError(
                f"A minimum purchase of ${promotion.min_purchase_amount} is required for this promotion."
            )
        
        return attrs
    
    def create(self, validated_data):
        """Create the DiscountQR and associated StoreDiscountUsage records."""
        promotion = validated_data['promotion_id']
        cart_items = validated_data['cart_items']
        user = self.context['request'].user
        
        with transaction.atomic():
            # Construct digital receipt data
            digital_receipt = self._build_digital_receipt(cart_items)
            
            # Create the master QR code
            discount_qr = DiscountQR.objects.create(
                promotion=promotion,
                generated_by_user=user,
                digital_receipt_data=digital_receipt
            )
            
            # Create placeholder StoreDiscountUsage records
            for store_id, store_data in digital_receipt['store_breakdown'].items():
                store = Store.objects.get(id=store_id)
                StoreDiscountUsage.objects.create(
                    discount_qr=discount_qr,
                    store=store,
                    store_cart_items=store_data['items'],
                    store_subtotal=store_data['subtotal']
                )
            
            logger.info(f"QR code {discount_qr.uuid} generated for user {user.username}")
            return discount_qr
    
    def _build_digital_receipt(self, cart_items):
        """Build the detailed digital receipt from cart items."""
        product_ids = [item['product_id'] for item in cart_items]
        products = Product.objects.filter(id__in=product_ids).select_related('store')
        product_map = {str(p.id): p for p in products}
        
        receipt_items = []
        store_breakdown = {}
        total_amount = 0
        
        for item in cart_items:
            product = product_map[item['product_id']]
            quantity = item['quantity']
            price = product.discounted_price
            item_total = price * quantity
            
            receipt_item = {
                'product_id': str(product.id),
                'name': product.name,
                'quantity': quantity,
                'price': str(price),
                'total': str(item_total),
                'store_info': {
                    'id': str(product.store.id),
                    'name': product.store.name
                }
            }
            receipt_items.append(receipt_item)
            total_amount += item_total
            
            # Aggregate by store
            store_id = str(product.store.id)
            if store_id not in store_breakdown:
                store_breakdown[store_id] = {'items': [], 'subtotal': 0}
            
            store_breakdown[store_id]['items'].append(receipt_item)
            store_breakdown[store_id]['subtotal'] += item_total
        
        return {
            'cart_items': receipt_items,
            'total_amount': str(total_amount),
            'store_breakdown': store_breakdown,
            'generated_at': timezone.now().isoformat()
        }


class DiscountQRSerializer(serializers.ModelSerializer):
    """
    Serializer for representing a master Discount QR code.
    
    Provides detailed information about the QR code, its status,
    and the associated promotion and digital receipt.
    """
    
    promotion = PromotionSerializer(read_only=True)
    is_valid = serializers.ReadOnlyField()
    
    class Meta:
        model = DiscountQR
        fields = [
            'uuid', 'promotion', 'generated_by_user', 'is_used',
            'expires_at', 'digital_receipt_data', 'generated_at',
            'is_valid'
        ]


class ValidateQRSerializer(serializers.Serializer):
    """
    Serializer for validating a Discount QR code at a store.
    
    Ensures that the store owner is authorized and the QR code
    is valid for use at their specific store.
    """
    
    qr_uuid = serializers.UUIDField(required=True)
    
    def validate_qr_uuid(self, value):
        """Validate the QR code and store owner's authorization."""
        try:
            qr_code = DiscountQR.objects.get(uuid=value)
            
            if not qr_code.is_valid:
                raise serializers.ValidationError("This QR code is invalid, expired, or already used.")
            
            store_owner = self.context['request'].user
            store = Store.objects.filter(owner=store_owner, is_active=True).first()
            
            if not store:
                raise serializers.ValidationError("You do not own an active store.")
            
            # Check if the store is part of this QR code's transaction
            if str(store.id) not in qr_code.digital_receipt_data.get('store_breakdown', {}):
                raise serializers.ValidationError("This QR code is not valid for your store.")
            
            # Check if this store has already used its portion
            usage = qr_code.store_usages.filter(store=store).first()
            if usage and usage.is_used_by_store:
                raise serializers.ValidationError("This QR code has already been used at your store.")
            
            self.context['qr_code'] = qr_code
            self.context['store'] = store
            return value
            
        except DiscountQR.DoesNotExist:
            raise serializers.ValidationError("Invalid QR code.")


class StoreDiscountUsageSerializer(serializers.ModelSerializer):
    """

    Serializer for tracking individual store usage of a master QR code.
    """
    store_name = serializers.CharField(source='store.name', read_only=True)
    
    class Meta:
        model = StoreDiscountUsage
        fields = [
            'store', 'store_name', 'is_used_by_store', 'used_at',
            'discount_applied', 'store_subtotal'
        ]


class UserDiscountQRDetailSerializer(DiscountQRSerializer):
    """
    Detailed QR code serializer for user-facing views.
    
    Includes the breakdown of usage by each store involved in the transaction.
    """
    store_usages = StoreDiscountUsageSerializer(many=True, read_only=True)
    
    class Meta(DiscountQRSerializer.Meta):
        fields = DiscountQRSerializer.Meta.fields + ['store_usages']
