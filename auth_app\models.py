"""
User authentication models for Best on Click e-commerce platform.

This module defines the custom User model with role-based access control,
supporting regular users, store owners, and system administrators.
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
import uuid


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser.
    
    Supports three user types: Customer, Store Owner, and Admin.
    Includes additional fields for enhanced user management and AI personalization.
    """
    
    USER_TYPE_CHOICES = [
        ('customer', 'Customer'),
        ('store_owner', 'Store Owner'),
        ('admin', 'System Administrator'),
    ]
    
    # Core user information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_type = models.CharField(
        max_length=20, 
        choices=USER_TYPE_CHOICES, 
        default='customer',
        help_text="User role determining access permissions"
    )
    
    # Enhanced profile information
    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'Enter a valid phone number.')],
        blank=True,
        null=True,
        help_text="Contact phone number for notifications and support"
    )
    
    date_of_birth = models.DateField(
        blank=True, 
        null=True,
        help_text="Used for age-based recommendations and compliance"
    )
    
    # AI personalization fields
    preferred_categories = models.JSONField(
        default=list,
        blank=True,
        help_text="User's preferred product categories for AI recommendations"
    )
    
    ai_consent = models.BooleanField(
        default=False,
        help_text="User consent for AI-powered personalization and data analysis"
    )
    
    # Account status and metadata
    is_verified = models.BooleanField(
        default=False,
        help_text="Email verification status"
    )
    
    last_activity = models.DateTimeField(
        auto_now=True,
        help_text="Last user activity timestamp for session management"
    )
    
    # Store owner specific fields
    store_name = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text="Store name for store owners"
    )
    
    business_license = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Business license number for store owners"
    )
    
    class Meta:
        db_table = 'auth_users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        indexes = [
            models.Index(fields=['user_type']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['last_activity']),
        ]
    
    def __str__(self):
        return f"{self.username} ({self.get_user_type_display()})"
    
    @property
    def is_customer(self):
        """Check if user is a customer."""
        return self.user_type == 'customer'
    
    @property
    def is_store_owner(self):
        """Check if user is a store owner."""
        return self.user_type == 'store_owner'
    
    @property
    def is_system_admin(self):
        """Check if user is a system administrator."""
        return self.user_type == 'admin'
    
    def get_full_display_name(self):
        """Return user's full display name or username as fallback."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username


class UserProfile(models.Model):
    """
    Extended user profile for additional user information.
    
    Separated from User model to keep authentication model lean
    while providing rich profile data for AI personalization.
    """
    
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE, 
        related_name='profile'
    )
    
    # Address information
    address_line_1 = models.CharField(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True, default='USA')
    
    # Preferences for AI recommendations
    budget_range_min = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        blank=True, 
        null=True,
        help_text="Minimum budget for product recommendations"
    )
    
    budget_range_max = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        blank=True, 
        null=True,
        help_text="Maximum budget for product recommendations"
    )
    
    # Marketing preferences
    email_notifications = models.BooleanField(
        default=True,
        help_text="Receive email notifications about promotions and updates"
    )
    
    sms_notifications = models.BooleanField(
        default=False,
        help_text="Receive SMS notifications about orders and promotions"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
    
    def __str__(self):
        return f"Profile for {self.user.username}"
    
    @property
    def full_address(self):
        """Return formatted full address."""
        address_parts = [
            self.address_line_1,
            self.address_line_2,
            self.city,
            self.state,
            self.postal_code,
            self.country
        ]
        return ', '.join(filter(None, address_parts))
